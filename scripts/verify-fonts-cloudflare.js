#!/usr/bin/env node

/**
 * Cloudflare Pages Font Verification Script
 * 验证字体文件在Cloudflare Pages部署后的加载情况
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 开始验证Cloudflare Pages字体配置...')

const DIST_DIR = path.join(__dirname, '..', 'dist')
const STATIC_DIR = path.join(__dirname, '..', 'static')

/**
 * 检查字体文件是否存在
 */
function checkFontFiles() {
  console.log('📁 检查字体文件...')
  
  const fontFiles = [
    'MatrixBoldSmallCaps.ttf',
    'en.ttf',
    'zh.ttf',
    'cn.ttf',
    'jp.ttf',
    'jp2.otf',
    'en2.ttf',
    'en3.ttf',
    'link.ttf'
  ]

  const staticFontsDir = path.join(STATIC_DIR, 'fonts')
  const distFontsDir = path.join(DIST_DIR, 'fonts')

  console.log('📂 检查源字体文件 (static/fonts):')
  fontFiles.forEach(font => {
    const fontPath = path.join(staticFontsDir, font)
    if (fs.existsSync(fontPath)) {
      const stats = fs.statSync(fontPath)
      console.log(`  ✅ ${font} (${(stats.size / 1024).toFixed(2)} KB)`)
    } else {
      console.log(`  ❌ ${font} - 文件不存在`)
    }
  })

  console.log('\n📂 检查构建后字体文件 (dist/fonts):')
  if (fs.existsSync(distFontsDir)) {
    const distFonts = fs.readdirSync(distFontsDir)
    distFonts.forEach(font => {
      const fontPath = path.join(distFontsDir, font)
      const stats = fs.statSync(fontPath)
      console.log(`  ✅ ${font} (${(stats.size / 1024).toFixed(2)} KB)`)
    })
  } else {
    console.log('  ⚠️  dist/fonts 目录不存在')
  }
}

/**
 * 检查字体CSS文件
 */
function checkFontCSS() {
  console.log('\n🎨 检查字体CSS配置...')
  
  const fontCSSPath = path.join(STATIC_DIR, 'fonts', 'font-face.css')
  const distFontCSSPath = path.join(DIST_DIR, 'fonts', 'font-face.css')

  if (fs.existsSync(fontCSSPath)) {
    console.log('  ✅ 源字体CSS文件存在')
    
    const content = fs.readFileSync(fontCSSPath, 'utf8')
    
    // 检查关键字体定义
    const criticalFonts = ['MatrixBoldSmallCaps', 'en', 'zh', 'jp']
    criticalFonts.forEach(font => {
      if (content.includes(`font-family: "${font}"`)) {
        console.log(`    ✅ ${font} 字体定义存在`)
      } else {
        console.log(`    ❌ ${font} 字体定义缺失`)
      }
    })

    // 检查font-display设置
    if (content.includes('font-display: swap')) {
      console.log('    ✅ font-display: swap 配置正确')
    } else {
      console.log('    ⚠️  缺少 font-display: swap 配置')
    }

    // 检查unicode-range设置
    if (content.includes('unicode-range:')) {
      console.log('    ✅ unicode-range 配置存在')
    } else {
      console.log('    ⚠️  缺少 unicode-range 配置')
    }
  } else {
    console.log('  ❌ 源字体CSS文件不存在')
  }

  if (fs.existsSync(distFontCSSPath)) {
    console.log('  ✅ 构建后字体CSS文件存在')
  } else {
    console.log('  ❌ 构建后字体CSS文件不存在')
  }
}

/**
 * 检查HTML中的字体预加载
 */
function checkFontPreloads() {
  console.log('\n🔗 检查字体预加载配置...')
  
  const indexPath = path.join(DIST_DIR, 'index.html')
  
  if (fs.existsSync(indexPath)) {
    const content = fs.readFileSync(indexPath, 'utf8')
    
    const preloadFonts = [
      'MatrixBoldSmallCaps.ttf',
      'en.ttf',
      'zh.ttf',
      'jp.ttf'
    ]

    preloadFonts.forEach(font => {
      if (content.includes(`href="/fonts/${font}"`) && content.includes('rel="preload"')) {
        console.log(`  ✅ ${font} 预加载配置正确`)
      } else {
        console.log(`  ❌ ${font} 预加载配置缺失`)
      }
    })

    // 检查Google Fonts预连接
    if (content.includes('preconnect') && content.includes('fonts.googleapis.com')) {
      console.log('  ✅ Google Fonts 预连接配置正确')
    } else {
      console.log('  ⚠️  Google Fonts 预连接配置可能缺失')
    }
  } else {
    console.log('  ❌ index.html 文件不存在')
  }
}

/**
 * 检查_headers文件中的字体配置
 */
function checkFontHeaders() {
  console.log('\n📋 检查字体HTTP头配置...')
  
  const headersPath = path.join(__dirname, '..', '_headers')
  const distHeadersPath = path.join(DIST_DIR, '_headers')

  if (fs.existsSync(headersPath)) {
    const content = fs.readFileSync(headersPath, 'utf8')
    
    // 检查字体MIME类型
    const fontMimeTypes = ['font/ttf', 'font/otf', 'font/woff', 'font/woff2']
    fontMimeTypes.forEach(mimeType => {
      if (content.includes(mimeType)) {
        console.log(`  ✅ ${mimeType} MIME类型配置正确`)
      } else {
        console.log(`  ❌ ${mimeType} MIME类型配置缺失`)
      }
    })

    // 检查字体缓存配置
    if (content.includes('/fonts/*') && content.includes('max-age=31536000')) {
      console.log('  ✅ 字体缓存配置正确')
    } else {
      console.log('  ❌ 字体缓存配置缺失或不正确')
    }

    // 检查CORS配置
    if (content.includes('Access-Control-Allow-Origin: *')) {
      console.log('  ✅ 字体CORS配置正确')
    } else {
      console.log('  ⚠️  字体CORS配置可能缺失')
    }
  } else {
    console.log('  ❌ _headers 文件不存在')
  }

  if (fs.existsSync(distHeadersPath)) {
    console.log('  ✅ 构建后_headers文件存在')
  } else {
    console.log('  ❌ 构建后_headers文件不存在')
  }
}

/**
 * 生成字体加载测试页面
 */
function generateFontTestPage() {
  console.log('\n🧪 生成字体测试页面...')

  const testPageContent = `<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体加载测试 - Yu-Gi-Oh Card Maker</title>
    <link rel="stylesheet" href="/fonts/font-face.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .font-test {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .font-test h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .font-en { font-family: var(--font-en); font-size: 18px; }
        .font-zh { font-family: var(--font-zh); font-size: 18px; }
        .font-ja { font-family: var(--font-ja); font-size: 18px; }
        .font-matrix { font-family: var(--font-matrix); font-size: 20px; }
        .status-panel {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .status-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .font-info {
            font-size: 14px;
            color: #666;
            margin-top: 10px;
        }
        .test-section {
            border-left: 4px solid #007bff;
            padding-left: 20px;
            margin: 30px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Yu-Gi-Oh Card Maker 字体加载测试</h1>
        <p>此页面用于验证Cloudflare Pages部署后的字体加载情况</p>

        <div class="status-panel">
            <h3>📊 字体加载状态</h3>
            <div id="font-status">正在检测字体加载状态...</div>
        </div>

        <div class="test-section">
            <h2>🔤 字体显示测试</h2>

            <div class="font-test">
                <h3>英文字体测试 (English Font)</h3>
                <p class="font-en">This is a test of English fonts. ABCDEFGHIJKLMNOPQRSTUVWXYZ</p>
                <p class="font-en">abcdefghijklmnopqrstuvwxyz 0123456789</p>
                <div class="font-info">字体栈: var(--font-en) → "en", "en2", "en3", "Arial", "Helvetica", sans-serif</div>
            </div>

            <div class="font-test">
                <h3>中文字体测试 (Chinese Font)</h3>
                <p class="font-zh">这是中文字体测试。游戏王卡片制作器支持中文显示。</p>
                <p class="font-zh">青眼白龙 黑魔导师 真红眼黑龙 艾克佐迪亚</p>
                <div class="font-info">字体栈: var(--font-zh) → "zh", "cn", "Noto Sans SC", "Microsoft YaHei", sans-serif</div>
            </div>

            <div class="font-test">
                <h3>日文字体测试 (Japanese Font)</h3>
                <p class="font-ja">これは日本語フォントのテストです。遊戯王カードメーカー。</p>
                <p class="font-ja">ブルーアイズ・ホワイト・ドラゴン ブラック・マジシャン</p>
                <div class="font-info">字体栈: var(--font-ja) → "jp", "jp2", "Noto Sans JP", "Hiragino Sans", sans-serif</div>
            </div>

            <div class="font-test">
                <h3>Matrix字体测试 (Matrix Font)</h3>
                <p class="font-matrix">MATRIX BOLD SMALL CAPS FONT TEST</p>
                <p class="font-matrix">YU-GI-OH CARD MAKER TITLE FONT</p>
                <div class="font-info">字体栈: var(--font-matrix) → "MatrixBoldSmallCaps", "Arial Black", sans-serif</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 技术信息</h2>
            <div id="tech-info">
                <p><strong>用户代理:</strong> <span id="user-agent"></span></p>
                <p><strong>页面加载时间:</strong> <span id="load-time"></span></p>
                <p><strong>字体CSS状态:</strong> <span id="css-status">检测中...</span></p>
            </div>
        </div>
    </div>

    <script>
        const startTime = performance.now();

        // 显示用户代理信息
        document.getElementById('user-agent').textContent = navigator.userAgent;

        // 检查字体CSS是否加载
        function checkFontCSS() {
            const link = document.querySelector('link[href="/fonts/font-face.css"]');
            if (link) {
                document.getElementById('css-status').innerHTML = '<span style="color: green;">✅ 字体CSS已加载</span>';
            } else {
                document.getElementById('css-status').innerHTML = '<span style="color: red;">❌ 字体CSS未找到</span>';
            }
        }

        // 检查字体加载状态
        function updateFontStatus() {
            const statusDiv = document.getElementById('font-status');
            const testFonts = [
                { name: 'en', display: '英文字体 (en.ttf)' },
                { name: 'zh', display: '中文字体 (zh.ttf)' },
                { name: 'jp', display: '日文字体 (jp.ttf)' },
                { name: 'MatrixBoldSmallCaps', display: 'Matrix字体 (MatrixBoldSmallCaps.ttf)' }
            ];

            let statusHTML = '';
            let allLoaded = true;

            testFonts.forEach(font => {
                const isLoaded = document.fonts.check(\`16px "\${font.name}"\`);
                const statusClass = isLoaded ? 'status-success' : 'status-error';
                const statusIcon = isLoaded ? '✅' : '❌';
                statusHTML += \`<div class="status-item \${statusClass}">\${statusIcon} \${font.display}: \${isLoaded ? '已加载' : '未加载'}</div>\`;
                if (!isLoaded) allLoaded = false;
            });

            // 添加总体状态
            const overallClass = allLoaded ? 'status-success' : 'status-error';
            const overallIcon = allLoaded ? '🎉' : '⚠️';
            statusHTML = \`<div class="status-item \${overallClass}">\${overallIcon} 总体状态: \${allLoaded ? '所有字体加载成功' : '部分字体加载失败'}</div>\` + statusHTML;

            statusDiv.innerHTML = statusHTML;
        }

        // 页面加载完成后执行检查
        document.addEventListener('DOMContentLoaded', () => {
            checkFontCSS();
            updateFontStatus();

            const loadTime = (performance.now() - startTime).toFixed(2);
            document.getElementById('load-time').textContent = loadTime + ' ms';
        });

        // 字体加载完成后再次检查
        document.fonts.ready.then(() => {
            console.log('🎉 所有字体已加载完成');
            updateFontStatus();

            // 详细日志
            document.fonts.forEach(font => {
                console.log('已加载字体:', font.family, font.status);
            });

            // 网络请求检查
            fetch('/fonts/font-face.css')
                .then(response => {
                    console.log('字体CSS响应状态:', response.status, response.statusText);
                    console.log('Content-Type:', response.headers.get('content-type'));
                })
                .catch(error => {
                    console.error('字体CSS加载失败:', error);
                });
        });

        // 定期更新状态（用于调试）
        setInterval(updateFontStatus, 2000);
    </script>
</body>
</html>`

  const testPagePath = path.join(DIST_DIR, 'font-test.html')
  
  try {
    fs.writeFileSync(testPagePath, testPageContent, 'utf8')
    console.log('  ✅ 字体测试页面已生成: /font-test.html')
    console.log('  📝 部署后可访问: https://your-domain.pages.dev/font-test.html')
  } catch (error) {
    console.log('  ❌ 生成字体测试页面失败:', error.message)
  }
}

/**
 * 主函数
 */
function main() {
  try {
    checkFontFiles()
    checkFontCSS()
    checkFontPreloads()
    checkFontHeaders()
    generateFontTestPage()
    
    console.log('\n🎉 字体验证完成!')
    console.log('\n📋 建议的后续步骤:')
    console.log('1. 确保所有字体文件都已正确复制到dist目录')
    console.log('2. 验证_headers文件包含正确的MIME类型配置')
    console.log('3. 部署后访问 /font-test.html 页面测试字体加载')
    console.log('4. 使用浏览器开发者工具检查字体文件的网络请求状态')
    console.log('5. 验证字体文件的Content-Type响应头是否正确')
    
  } catch (error) {
    console.error('❌ 字体验证过程中出现错误:', error.message)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = { checkFontFiles, checkFontCSS, checkFontPreloads, checkFontHeaders }
