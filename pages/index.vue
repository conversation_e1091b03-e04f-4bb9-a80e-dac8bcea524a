<template>
  <div id="app">
    <!-- Navigation Header -->
    <header role="banner">
      <b-navbar type="dark" fixed="top" class="main-navbar" expand="lg">
        <b-navbar-brand href="#card-editor" class="navbar-brand-custom">
          <div class="brand-container">
            <img src="/yugioh-card-maker.png" alt="Yu-Gi-Oh! Card Maker Logo" class="brand-logo" />
            <div class="brand-text">
              <h1 class="brand-title mb-0">
                <span class="brand-main">Yu-Gi-Oh! Card Maker</span>
                <small class="brand-sub d-block">Professional Card Creator</small>
              </h1>
            </div>
          </div>
        </b-navbar-brand>

        <!-- Desktop Navigation Menu - Hidden on Mobile -->
        <b-collapse id="nav-collapse" is-nav class="d-none d-lg-flex">
          <b-navbar-nav class="mx-auto">
            <b-nav-item href="#card-editor" class="nav-link-custom">
              <fa :icon="['fas', 'edit']" class="me-1" />
              {{ $t('nav.editor') }}
            </b-nav-item>
            <b-nav-item href="#features" class="nav-link-custom">
              <fa :icon="['fas', 'star']" class="me-1" />
              {{ $t('nav.features') }}
            </b-nav-item>
            <b-nav-item href="#how-to-use" class="nav-link-custom">
              <fa :icon="['fas', 'question-circle']" class="me-1" />
              {{ $t('nav.howToUse') }}
            </b-nav-item>
            <b-nav-item href="#faq" class="nav-link-custom">
              <fa :icon="['fas', 'info-circle']" class="me-1" />
              {{ $t('nav.faq') }}
            </b-nav-item>
          </b-navbar-nav>
        </b-collapse>

        <!-- Mobile & Desktop Right Side Controls -->
        <div class="navbar-right-controls">
          <!-- Language Switcher - Always Visible -->
          <LanguageSwitcher @language-changed="onLanguageChanged" class="language-switcher" />
        </div>
      </b-navbar>


    </header>

    <!-- Main Card Editor Section -->
    <main id="card-editor" class="card-editor-section anchor-section" role="main">
      <div class="container-fluid">
        <div class="row justify-content-center">

          <!-- Left Ad Space - Hidden for now -->
          <div class="col-xl-1 d-none d-xl-block" style="display: none !important;">
            <div class="ad-space left-ad">
              <!-- 预留左侧广告位 -->
            </div>
          </div>

          <!-- Card Preview Panel -->
          <aside class="col-lg-6 col-xl-6 preview-panel" role="complementary">
            <div class="preview-container">
              <div class="card-preview-wrapper">
                <div
                  id="yugiohcard-wrap"
                  ref="yugiohcard-wrap"
                  class="card-display"
                  @mousemove="move"
                  @mouseleave="leave"
                  role="img"
                  :aria-label="cardTitle || 'Yu-Gi-Oh! Card Preview'"
                >
                  <canvas
                    id="yugiohcard"
                    ref="yugiohcard"
                    class="card-canvas"
                    :aria-label="`${cardTitle || 'Untitled Card'} - ${cardType} Card`"
                  ></canvas>
                </div>
              </div>
            </div>
          </aside>

          <!-- Card Editor Panel -->
          <section class="col-lg-6 col-xl-6 editor-panel" role="form">
            <div class="editor-container">
              <!-- 可滚动的表单内容区域 -->
              <div class="editor-content scrollable-content">
                <!-- Card Settings Form -->
                <form @submit.prevent="doDrawCard" class="card-settings-form">

                  <!-- Basic Settings Row -->
              <!-- 防偽貼 -->
              <b-row class="my-3">
                <b-col cols="12" sm="6" lg="4" class="px-2">
                  <div class="form-check px-0">
                    <label>{{ ui[uiLang].square_foil_stamp }}</label>
                    <b-form-checkbox
                      v-model="holo"
                      :class="{'checkbox-wrap': true, 'active': holo}"
                      button
                    >{{ holo ? ui[uiLang].on : ui[uiLang].off }}</b-form-checkbox>
                  </div>
                </b-col>
                <!-- 稀有度 -->
                <b-col cols="12" sm="6" lg="4" class="px-2">
                  <label>{{ ui[uiLang].rarity }}</label>
                  <b-form-select v-model="cardRare" :options="cardRareOpts"></b-form-select>
                </b-col>
                <!-- 卡名色 -->
                <b-col cols="12" sm="6" lg="4" class="px-2">
                  <label>{{ ui[uiLang].title_color }}</label>
                  <b-form-input v-model="titleColor" type="color"></b-form-input>
                </b-col>
              </b-row>

                  <!-- Card Information -->
              <!-- 卡片密碼 - Hidden -->
              <b-row class="my-3" style="display: none;">
                <b-col cols="6" lg="4" class="px-2">
                  <div class="form-check px-0">
                    <label>{{ ui[uiLang].card_secret }}</label>
                    <b-form-checkbox
                      v-model="cardLoadYgoProEnabled"
                      :class="{'checkbox-wrap': true, 'active': cardLoadYgoProEnabled}"
                      button
                    >{{ ui[uiLang].auto_fill_card_data }}</b-form-checkbox>
                  </div>
                </b-col>
                <b-col cols="6" lg="8" class="px-2">
                  <label><small>{{ ui[uiLang].card_secret_note }}</small></label>
                  <b-form-input
                    v-model="cardKey"
                    type="number"
                    maxlength="8"
                    :placeholder="ui[uiLang].plz_input_card_secret"
                  />
                </b-col>
              </b-row>

              <!-- 卡片名稱 -->
              <b-row class="my-3">
                <b-col class="px-2">
                  <label>{{ ui[uiLang].card_name }}</label>
                  <b-form-input v-model="cardTitle"></b-form-input>
                </b-col>
              </b-row>

              <!-- 卡圖 -->
              <b-row class="my-3">
                <b-col class="px-2">
                  <b-form-file
                    v-model="cardImg"
                    :state="Boolean(cardImg)"
                    :placeholder="ui[uiLang].upload_image"
                    browse="✚"
                    accept="image/*"
                    :drop-placeholder="ui[uiLang].drag_and_drop"
                  ></b-form-file>
                </b-col>
              </b-row>

              <!-- 卡種、卡面、效果 -->
              <b-row class="my-3">
                <!-- 卡種 -->
                <b-col cols="12" sm="6" lg="3" class="px-2">
                  <label>{{ ui[uiLang].card_type }}</label>
                  <b-form-select v-model="cardType" :options="cardTypeOpts"></b-form-select>
                </b-col>

                <!-- 卡面 -->
                <b-col cols="12" sm="6" lg="3" class="px-2">
                  <label>{{ ui[uiLang].card_subtype }}</label>
                  <b-form-select v-model="cardSubtype" :options="cardSubtypeOpts[cardType]"></b-form-select>
                </b-col>

                <!-- 效果 -->
                <b-col v-show="cardType==='Monster'" cols="12" sm="6" lg="3" class="px-2">
                  <label>{{ ui[uiLang].card_effect }}</label>
                  <b-form-select v-model="cardEff1" :options="cardEff1Opts"></b-form-select>
                </b-col>
                <b-col v-show="cardType==='Monster'" cols="12" sm="6" lg="3" class="px-2">
                  <label>&emsp;</label>
                  <b-form-select v-model="cardEff2" :options="cardEff2Opts"></b-form-select>
                </b-col>
              </b-row>

              <!-- 屬性、種族 -->
              <b-row v-show="cardType==='Monster'" class="my-3">
                <!-- 屬性 -->
                <b-col cols="12" sm="6" lg="4" class="px-2">
                  <label>{{ ui[uiLang].card_attribute }}</label>
                  <b-form-select v-model="cardAttr" :options="cardAttrOpts"></b-form-select>
                </b-col>

                <!-- 種族切换 -->
                <b-col cols="12" sm="6" lg="4" class="px-2">
                  <div class="form-check px-0">
                    <label>{{ ui[uiLang].card_race_type }}</label>
                    <b-form-checkbox
                      v-model="cardCustomRaceEnabled"
                      :class="{'checkbox-wrap': true, 'active': cardCustomRaceEnabled}"
                      button
                    >{{ ui[uiLang].custom }}</b-form-checkbox>
                  </div>
                </b-col>

                <!-- 種族 - 種族選擇 -->
                <b-col v-show="!cardCustomRaceEnabled" cols="6" lg="3" class="px-2">
                  <label>&emsp;</label>
                  <b-form-select v-model="cardRace" :options="cardRaceOpts"></b-form-select>
                </b-col>
                <!-- 種族 - 自訂輸入 -->
                <b-col v-show="cardCustomRaceEnabled" cols="6" lg="3" class="px-2">
                  <label>&emsp;</label>
                  <b-form-input 
                    v-model="cardCustomRace"
                    type="text"
                    maxlength="8"
                    :placeholder="ui[uiLang].plz_input_race_type"
                  />
                </b-col>
              </b-row>

              <!-- 靈擺、特殊召喚、等級 -->
              <b-row class="my-3">
                <!-- 靈擺 -->
                <b-col v-show="canPendulumEnabled" cols="12" sm="6" lg="4" class="px-2">
                  <div class="form-check px-0">
                    <label>{{ ui[uiLang].pendulum }}</label>
                    <b-form-checkbox
                      v-model="Pendulum"
                      :class="{'checkbox-wrap': true, 'active': Pendulum}"
                      button
                    >{{ ui[uiLang].pendulum }}</b-form-checkbox>
                  </div>
                </b-col>

                <!-- 特殊召喚 -->
                <b-col v-show="cardType==='Monster'" cols="12" sm="6" lg="4" class="px-2">
                  <div class="form-check px-0">
                    <label>{{ ui[uiLang].special_summon }}</label>
                    <b-form-checkbox
                      v-model="Special"
                      :class="{'checkbox-wrap': true, 'active': Special}"
                      button
                    >{{ ui[uiLang].special_summon }}</b-form-checkbox>
                  </div>
                </b-col>

                <!-- 等級 -->
                <b-col v-show="cardType==='Monster' && !isLinkMonster" cols="12" sm="12" lg="4" class="px-2">
                  <label>{{ ui[uiLang].lavel_and_rank }}</label>
                  <b-form-select v-model="cardLevel" :options="cardLevelOpts"></b-form-select>
                </b-col>
              </b-row>

              <!-- 靈擺效果區 -->
              <b-row v-show="Pendulum" class="my-3">
                <b-col cols="12">
                  <h4 class="text-light text-center">{{ ui[uiLang].pendulum_area }}</h4>
                </b-col>
                <b-col cols="12">
                  <b-row class="mb-3">
                    <b-col cols="4" class="px-2">
                      <label>{{ ui[uiLang].pendulum_blue }}</label>
                      <b-form-input v-model="cardBLUE" type="number" min="0" max="12"></b-form-input>
                    </b-col>

                    <b-col cols="4" class="px-2">
                      <label>{{ ui[uiLang].pendulum_red }}</label>
                      <b-form-input v-model="cardRED" type="number" min="0" max="12"></b-form-input>
                    </b-col>

                    <b-col cols="4" class="px-2">
                      <label>{{ ui[uiLang].text_size }}</label>
                      <b-form-input v-model="pendulumSize" type="number"></b-form-input>
                    </b-col>
                  </b-row>

                  <b-row class="my-3">
                    <b-col class="px-2">
                      <label>{{ ui[uiLang].pendulum_effect_text }}</label>
                      <b-form-textarea v-model="cardPendulumInfo" rows="5"></b-form-textarea>
                    </b-col>
                  </b-row>
                </b-col>
              </b-row>

              <!-- 攻守區 -->
              <b-row class="my-3">
                <!-- 攻擊力 -->
                <b-col v-show="cardType==='Monster'" cols="12" sm="6" lg="3" class="px-2">
                  <label>{{ ui[uiLang].attack }}</label>
                  <b-form-input v-model="cardATK" type="text" maxlength="6"></b-form-input>
                </b-col>

                <!-- 守備力 -->
                <b-col v-show="cardType==='Monster' && !isLinkMonster" cols="12" sm="6" lg="3" class="px-2">
                  <label>{{ ui[uiLang].defence }}</label>
                  <b-form-input v-model="cardDEF" type="text" maxlength="6"></b-form-input>
                </b-col>

                <!-- 連結區 -->
                <b-col v-show="isLinkMonster" cols="12" sm="6" lg="6" class="px-2">
                  <label>{{ ui[uiLang].link }}</label>
                  <table class="link-table">
                    <tr v-for="row in [0,1,2]" :key="row">
                      <td v-for="col in [1,2,3]" :key="col">
                        <b-form-checkbox
                          v-if="row*3+col!==5"
                          v-model="links[row*3+col].val"
                          :class="{'checkbox-wrap': true, 'active': links[row*3+col].val}"
                          button
                        >{{ links[row*3+col].symbol }}</b-form-checkbox>
                      </td>
                    </tr>
                  </table>
                </b-col>

                <!-- 文字大小 -->
                <b-col cols="12" sm="6" lg="3" class="px-2">
                  <label>{{ ui[uiLang].text_size }}</label>
                  <b-form-input v-model="infoSize" type="number"></b-form-input>
                </b-col>
              </b-row>

              <!-- 卡片說明 -->
              <b-row class="my-3">
                <b-col class="px-2">
                  <label>{{ ui[uiLang].card_info_text }}</label>
                  <b-form-textarea v-model="cardInfo" rows="7"></b-form-textarea>
                </b-col>
              </b-row>
              
                </form>
              </div>

              <!-- 固定底部操作区域 -->
              <div class="editor-footer fixed-bottom-controls">
                <!-- 主要操作按鈕組 -->
                <div class="button-group d-flex flex-wrap gap-2 align-items-center justify-content-center mb-2">
                  <button
                    type="button"
                    class="btn btn-info"
                    @click="doDrawCard"
                    :aria-label="ui[uiLang].generate"
                  >
                    <fa :icon="['fas', 'magic']" class="me-1" />
                    {{ ui[uiLang].generate }}
                  </button>

                  <button
                    type="button"
                    class="btn btn-success"
                    @click="download_img"
                    :aria-label="ui[uiLang].download"
                  >
                    <fa :icon="['fas', 'download']" class="me-1" />
                    {{ ui[uiLang].download }}
                  </button>

                  <button
                    type="button"
                    class="btn btn-outline-danger"
                    @click="load_default_data"
                    :aria-label="ui[uiLang].reset_to_default"
                  >
                    <fa :icon="['fas', 'undo']" class="me-1" />
                    {{ ui[uiLang].reset_to_default }}
                  </button>
                </div>

                <!-- 社交媒體分享按鈕組 -->
                <div class="social-share-row d-flex justify-content-center">
                  <SocialShareButtons />
                </div>

                <!-- <small class="text-muted d-block mt-2 text-center">
                  <fa :icon="['fas', 'info-circle']" class="me-1" />
                  {{ ui[uiLang].auto_gen_note }}
                </small> -->
              </div>
            </div>
          </section>

          <!-- Right Ad Space - Hidden for now -->
          <div class="col-xl-1 d-none d-xl-block" style="display: none !important;">
            <div class="ad-space right-ad">
              <!-- 预留右侧广告位 -->
            </div>
          </div>

        </div>
      </div>
    </main>

    <!-- What is Yu-Gi-Oh Card Maker Section -->
    <section id="what-is" class="what-is-section py-5 anchor-section" role="region">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-lg-6">
            <h2 class="section-title">
              {{ $t('sections.whatIs.title') }}
            </h2>
            <p class="section-description">
              {{ $t('sections.whatIs.description') }}
            </p>
            <div class="feature-highlights">
              <div class="highlight-item" v-for="(highlight, index) in whatIsHighlights" :key="index">
                <fa :icon="['fas', 'check-circle']" class="highlight-icon" />
                <span>{{ highlight }}</span>
              </div>
            </div>
          </div>
          <div class="col-lg-6">
            <div class="info-visual">
              <div class="visual-card">
                <fa :icon="['fas', 'magic']" class="visual-icon" />
                <h4>{{ $t('sections.whatIs.visualTitle') }}</h4>
                <p>{{ $t('sections.whatIs.visualDescription') }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Why Use Section -->
    <section id="why-use" class="why-use-section py-5 anchor-section" role="region">
      <div class="container">
        <div class="row">
          <div class="col-12 text-center mb-5">
            <h2 class="section-title">
              {{ $t('sections.whyUse.title') }}
            </h2>
            <p class="section-subtitle">
              {{ $t('sections.whyUse.subtitle') }}
            </p>
          </div>
        </div>
        <div class="row g-4">
          <div class="col-lg-4 col-md-6" v-for="(reason, index) in whyUseReasons" :key="index">
            <div class="reason-card h-100">
              <div class="reason-icon">
                <fa :icon="getReasonIcon(reason.icon)" />
              </div>
              <h3 class="reason-title">{{ reason.title }}</h3>
              <p class="reason-description">{{ reason.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- How to Create Cards Section -->
    <section id="how-to" class="how-to-section py-5 anchor-section" role="region">
      <div class="container">
        <div class="row">
          <div class="col-12 text-center mb-5">
            <h2 class="section-title">
              {{ $t('sections.howTo.title') }}
            </h2>
            <p class="section-subtitle">
              {{ $t('sections.howTo.subtitle') }}
            </p>
          </div>
        </div>
        <div class="row g-4">
          <div class="col-lg-3 col-md-6" v-for="(step, index) in howToSteps" :key="index">
            <div class="step-card h-100">
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-icon">
                <fa :icon="getStepIcon(step.icon)" />
              </div>
              <h3 class="step-title">{{ step.title }}</h3>
              <p class="step-description">{{ step.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section py-5 anchor-section" role="region">
      <div class="container">
        <div class="row">
          <div class="col-12 text-center mb-5">
            <h2 class="section-title">
              {{ $t('sections.features.title') }}
            </h2>
            <p class="section-subtitle">
              {{ $t('sections.features.subtitle') }}
            </p>
            <p class="section-description">
              {{ $t('sections.features.description') }}
            </p>
          </div>
        </div>

        <div class="row g-4 justify-content-center">
          <div class="col-lg-4 col-md-6 col-sm-12" v-for="(feature, index) in featuresData" :key="index">
            <div class="feature-card h-100">
              <div class="feature-icon">
                <fa :icon="getFeatureIcon(feature.icon)" />
              </div>
              <h3 class="feature-title">{{ feature.title }}</h3>
              <p class="feature-description">{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- How to Use Section -->
    <section id="how-to-use" class="how-to-use-section py-5 anchor-section" role="region">
      <div class="container">
        <div class="row">
          <div class="col-12 text-center mb-5">
            <h2 class="section-title">
              {{ $t('sections.howToUse.title') }}
            </h2>
            <p class="section-subtitle">
              {{ $t('sections.howToUse.subtitle') }}
            </p>
            <p class="section-description">
              {{ $t('sections.howToUse.description') }}
            </p>
          </div>
        </div>

        <div class="row g-4">
          <div class="col-md-4" v-for="(step, index) in stepsData" :key="index">
            <div class="step-card">
              <div class="step-number">{{ step.step }}</div>
              <h3 class="step-title">{{ step.title }}</h3>
              <p class="step-description">{{ step.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="faq-section py-5 anchor-section" role="region">
      <div class="container">
        <div class="row">
          <div class="col-12 text-center mb-5">
            <h2 class="section-title">
              {{ $t('sections.faq.title') }}
            </h2>
            <p class="section-subtitle">
              {{ $t('sections.faq.subtitle') }}
            </p>
            <p class="section-description">
              {{ $t('sections.faq.description') }}
            </p>
          </div>
        </div>

        <div class="row justify-content-center">
          <div class="col-lg-8">
            <div class="faq-list">
              <div class="faq-item" v-for="(faq, index) in faqData" :key="index" :class="{ 'active': activeFaqIndex === index }">
                <div class="faq-question" @click="toggleFaq(index)" role="button" tabindex="0" @keydown.enter="toggleFaq(index)" @keydown.space="toggleFaq(index)">
                  <h3 class="faq-question-text">{{ faq.question }}</h3>
                  <div class="faq-toggle-icon">
                    <fa :icon="['fas', activeFaqIndex === index ? 'chevron-up' : 'chevron-down']" />
                  </div>
                </div>
                <div class="faq-answer-wrapper" :class="{ 'expanded': activeFaqIndex === index }">
                  <div class="faq-answer">{{ faq.answer }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="site-footer py-5" role="contentinfo">
      <div class="container">
        <div class="row">
          <div class="col-md-6">
            <div class="footer-brand">
              <h3 class="footer-title h5 mb-3">Yu-Gi-Oh! Card Maker</h3>
              <p class="footer-description">
                {{ $t('footer.description') }}
              </p>
            </div>
          </div>

          <div class="col-md-6">
            <div class="footer-links">
              <h4 class="footer-links-title h6 mb-3">Legal</h4>
              <ul class="footer-nav">
                <li><nuxt-link to="/privacy" class="footer-link">Privacy Policy</nuxt-link></li>
                <li><nuxt-link to="/terms" class="footer-link">Terms of Service</nuxt-link></li>
              </ul>
            </div>
          </div>
        </div>

        <hr class="footer-divider my-4">

        <div class="row">
          <div class="col-12 text-center">
            <p class="copyright text-muted mb-0">
              © {{ new Date().getFullYear() }} yugiohcardmaker.org. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>

    <LoadingDialog />
  </div>
</template>

<script>
import { mapMutations, mapGetters, mapActions } from 'vuex'
import ui from '../static/lang.ui.json'
import cardMeta from '../static/lang.card_meta.json'
// 移除大型JSON文件的直接导入，改为异步加载
// import ygoproData from '../static/ygo/card_data.json'

// Import new components
import LanguageSwitcher from '~/components/LanguageSwitcher.vue'
import SocialShareButtons from '~/components/SocialShareButtons.vue'

export default {
  name: 'CardMakerPage',

  components: {
    LanguageSwitcher,
    SocialShareButtons
  },

  // Basic SEO head configuration - client-side SEO is handled by plugins/seo.js
  head() {
    // Return basic fallback for static generation
    return {
      title: 'Yu-Gi-Oh! Card Maker - Free Online Card Creator',
      htmlAttrs: {
        lang: 'en'
      },
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Create custom Yu-Gi-Oh! cards with our free online card maker. Design monsters, spells, and traps with professional quality tools. No sign up required!'
        }
      ]
    }
  },
  data() {
    return {
      adCollapsed: false,
      pageScrolling: 0,
      activeFaqIndex: null, // FAQ折叠/展开状态

      // 定时器引用，用于清理
      drawCardInterval: null,

      // Legacy support - will be replaced by Vuex
      uiLang: 'en',
      ui,
      cardLang: 'en',
      cardMeta,

      holo: true,
      cardRare: '0',
      cardRareOpts: {
        '0': 'N',
        '1': 'R',
        '2': 'UR',
      },
      titleColor: '#000000',
      cardLoadYgoProEnabled: true,
      cardKey: '',
      cardTitle: '',
      cardImg: null,
      cardType: 'Monster',
      cardSubtype: 'Normal',
      cardEff1: 'normal',
      cardEff2: 'none',
      cardAttr: 'LIGHT',
      cardCustomRaceEnabled: false,
      cardCustomRace: '',
      cardRace: 'dragon',
      Pendulum: true,
      Special: true,
      cardLevel: '12',
      cardLevelOpts: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      cardBLUE: 12,
      cardRED: 12,
      pendulumSize: 22, // 优化pendulum效果文本大小，与参考网站保持一致
      cardPendulumInfo: '',

      cardATK: '',
      cardDEF: '',
      links: {
        1: {val: false, symbol: '◤'},
        2: {val: false, symbol: '▲'},
        3: {val: false, symbol: '◥'},
        4: {val: false, symbol: '◀'},
        6: {val: false, symbol: '▶'},
        7: {val: false, symbol: '◣'},
        8: {val: false, symbol: '▼'},
        9: {val: false, symbol: '◢'},
      },
      infoSize: '20', // 优化卡片信息文本大小，与参考网站保持一致
      cardInfo: '',

      imgs: {},

      // 异步加载的数据
      ygoproData: null,
      ygoproDataLoading: false,
      ygoproDataLoaded: false,
    }
  },
  computed: {
    ...mapGetters([
      'currentLanguage',
      'currentUIData',
      'currentCardMeta',
      'currentSEOData'
    ]),

    // 安全的 UI 數據訪問
    safeCurrentUIData() {
      return this.currentUIData || {}
    },

    // 功能特色數據
    featuresData() {
      try {
        const features = this.$t('sections.features.items')
        return Array.isArray(features) ? features : [
          {
            title: 'All Card Types',
            description: 'Support for Monster, Spell, and Trap cards with all subtypes, including Pendulum and Link monsters.',
            icon: 'cards'
          },
          {
            title: 'Multi-language Support',
            description: 'Support for English, Japanese, and Chinese interfaces and card generation.',
            icon: 'globe'
          },
          {
            title: 'High Quality Output',
            description: 'Generate high-resolution card images using Canvas technology with direct download support.',
            icon: 'download'
          }
        ]
      } catch (e) {
        return [
          {
            title: 'All Card Types',
            description: 'Support for Monster, Spell, and Trap cards with all subtypes, including Pendulum and Link monsters.',
            icon: 'cards'
          },
          {
            title: 'Multi-language Support',
            description: 'Support for Chinese, Japanese, and English interfaces and card generation.',
            icon: 'globe'
          },
          {
            title: 'High-Quality Output',
            description: 'Generate high-resolution card images using Canvas technology with direct download support.',
            icon: 'download'
          }
        ]
      }
    },

    // 使用步驟數據
    stepsData() {
      try {
        const steps = this.$t('sections.howToUse.steps')
        return Array.isArray(steps) ? steps : [
          {
            step: '1',
            title: 'Choose Card Type',
            description: 'Select Monster, Spell, or Trap card and configure the appropriate attributes and effects.'
          },
          {
            step: '2',
            title: 'Customize Content',
            description: 'Upload card image, fill in card name, effect description and other detailed information.'
          },
          {
            step: '3',
            title: 'Generate & Download',
            description: 'Preview the card in real-time, then click download to get high-quality card image.'
          }
        ]
      } catch (e) {
        return [
          {
            step: '1',
            title: 'Choose Card Type',
            description: 'Select Monster, Spell, or Trap card and configure the appropriate attributes and effects.'
          },
          {
            step: '2',
            title: 'Customize Content',
            description: 'Upload card image, fill in card name, effect description and other detailed information.'
          },
          {
            step: '3',
            title: 'Generate & Download',
            description: 'Preview the card in real-time, then click download to get high-quality card image.'
          }
        ]
      }
    },

    // What Is 數據
    whatIsHighlights() {
      try {
        const highlights = this.$t('sections.whatIs.highlights')
        return Array.isArray(highlights) ? highlights : [
          'Professional Yu-Gi-Oh! card design tools',
          'Support for all card types and rarities',
          'High-resolution image generation',
          'Multi-language interface support',
          'Free to use with no registration required'
        ]
      } catch (e) {
        return [
          'Professional Yu-Gi-Oh! card design tools',
          'Support for all card types and rarities',
          'High-resolution image generation',
          'Multi-language interface support',
          'Free to use with no registration required'
        ]
      }
    },

    // Why Use 數據
    whyUseReasons() {
      try {
        const reasons = this.$t('sections.whyUse.items')
        return Array.isArray(reasons) ? reasons : [
          {
            title: 'Professional Quality',
            description: 'Create cards that look authentic with official Yu-Gi-Oh! templates and high-quality rendering.',
            icon: 'star'
          },
          {
            title: 'Easy to Use',
            description: 'Intuitive interface makes card creation simple for both beginners and experienced designers.',
            icon: 'user-friendly'
          },
          {
            title: 'Completely Free',
            description: 'All features available at no cost with no hidden fees or premium subscriptions.',
            icon: 'gift'
          },
          {
            title: 'No Registration',
            description: 'Start creating cards immediately without signing up or providing personal information.',
            icon: 'unlock'
          },
          {
            title: 'Multi-language',
            description: 'Support for multiple languages including English, Japanese, and Chinese.',
            icon: 'globe'
          },
          {
            title: 'High Resolution',
            description: 'Generate crisp, high-quality images perfect for printing or digital use.',
            icon: 'image'
          }
        ]
      } catch (e) {
        return [
          {
            title: 'Professional Quality',
            description: 'Create cards that look authentic with official Yu-Gi-Oh! templates and high-quality rendering.',
            icon: 'star'
          },
          {
            title: 'Easy to Use',
            description: 'Intuitive interface makes card creation simple for both beginners and experienced designers.',
            icon: 'user-friendly'
          },
          {
            title: 'Completely Free',
            description: 'All features available at no cost with no hidden fees or premium subscriptions.',
            icon: 'gift'
          }
        ]
      }
    },

    // How To 數據
    howToSteps() {
      try {
        const steps = this.$t('sections.howTo.items')
        return Array.isArray(steps) ? steps : [
          {
            title: 'Choose Card Type',
            description: 'Select whether you want to create a Monster, Spell, or Trap card.',
            icon: 'list'
          },
          {
            title: 'Upload Your Image',
            description: 'Add your custom artwork by uploading an image or dragging and dropping.',
            icon: 'upload'
          },
          {
            title: 'Fill Card Details',
            description: 'Enter card name, description, stats, and other properties using our intuitive form.',
            icon: 'edit'
          },
          {
            title: 'Download Your Card',
            description: 'Generate and download your custom Yu-Gi-Oh! card as a high-quality image.',
            icon: 'download'
          }
        ]
      } catch (e) {
        return [
          {
            title: 'Choose Card Type',
            description: 'Select whether you want to create a Monster, Spell, or Trap card.',
            icon: 'list'
          },
          {
            title: 'Upload Your Image',
            description: 'Add your custom artwork by uploading an image or dragging and dropping.',
            icon: 'upload'
          },
          {
            title: 'Fill Card Details',
            description: 'Enter card name, description, stats, and other properties using our intuitive form.',
            icon: 'edit'
          },
          {
            title: 'Download Your Card',
            description: 'Generate and download your custom Yu-Gi-Oh! card as a high-quality image.',
            icon: 'download'
          }
        ]
      }
    },

    // FAQ 數據
    faqData() {
      try {
        const faq = this.$t('sections.faq.items')
        return Array.isArray(faq) ? faq : [
          {
            question: 'Is this tool free to use?',
            answer: 'Yes, all features are completely free to use with no registration or payment required.'
          },
          {
            question: 'What card types are supported?',
            answer: 'All Yu-Gi-Oh! card types are supported, including Monster cards (Normal, Effect, Fusion, Ritual, Synchro, Xyz, Link, etc.), Spell cards, and Trap cards with all their subtypes.'
          },
          {
            question: 'Can I upload my own images?',
            answer: 'Yes, you can upload common image formats like JPG and PNG. The system will automatically adjust the size and position.'
          }
        ]
      } catch (e) {
        return [
          {
            question: 'Is this tool free to use?',
            answer: 'Yes, all features are completely free to use with no registration or payment required.'
          },
          {
            question: 'What card types are supported?',
            answer: 'All Yu-Gi-Oh! card types are supported, including Monster cards (Normal, Effect, Fusion, Ritual, Synchro, Xyz, Link, etc.), Spell cards, and Trap cards with all their subtypes.'
          },
          {
            question: 'Can I upload my own images?',
            answer: 'Yes, you can upload common image formats like JPG and PNG. The system will automatically adjust the size and position.'
          }
        ]
      }
    },

    uiLangOpts() {
      return Object.fromEntries(Object.keys(this.ui).map(key=> [key, this.ui[key].name || key]))
    },
    cardLangOpts() {
      return Object.fromEntries(Object.keys(this.cardMeta).map(key=> [key, this.cardMeta[key].name || key]))
    },
    cardTypeOpts() {
      const uiData = this.ui && this.ui[this.uiLang] ? this.ui[this.uiLang] : {}
      return {
        'Monster': uiData.monster_card || 'Monster',
        'Spell': uiData.spell_card || 'Spell',
        'Trap': uiData.trap_card || 'Trap',
      }
    },
    cardSubtypeOpts() {
      return {
        "Monster": {
          'Normal': this.ui[this.uiLang].m_card.normal,
          'Effect': this.ui[this.uiLang].m_card.effect,
          'Fusion': this.ui[this.uiLang].m_card.fusion,
          'Ritual': this.ui[this.uiLang].m_card.ritual,
          'Synchro': this.ui[this.uiLang].m_card.synchro,
          'Xyz': this.ui[this.uiLang].m_card.xyz,
          'Link': this.ui[this.uiLang].m_card.link,
          'Token': this.ui[this.uiLang].m_card.token,
          'Slifer': this.ui[this.uiLang].m_card.slifer,
          'Ra': this.ui[this.uiLang].m_card.ra,
          'Obelisk': this.ui[this.uiLang].m_card.obelisk,
          'LDragon': this.ui[this.uiLang].m_card.ldragon,
        },
        "Spell": {
          'Normal': this.ui[this.uiLang].st_card.normal,
          'Continuous': this.ui[this.uiLang].st_card.continuous,
          'Field': this.ui[this.uiLang].st_card.field,
          'Equip': this.ui[this.uiLang].st_card.equip,
          'Quick': this.ui[this.uiLang].st_card.quick,
          'Ritual': this.ui[this.uiLang].st_card.ritual,
        },
        "Trap": {
          'Normal': this.ui[this.uiLang].st_card.normal,
          'Continuous': this.ui[this.uiLang].st_card.continuous,
          'Counter': this.ui[this.uiLang].st_card.counter,
        }
      }
    },
    cardEffOpts() {
      return {
        'none': this.ui[this.uiLang].card_effect_opts.none,
        'normal': this.ui[this.uiLang].card_effect_opts.normal,
        'toon': this.ui[this.uiLang].card_effect_opts.toon,
        'spirit': this.ui[this.uiLang].card_effect_opts.spirit,
        'union': this.ui[this.uiLang].card_effect_opts.union,
        'gemini': this.ui[this.uiLang].card_effect_opts.gemini,
        'flip': this.ui[this.uiLang].card_effect_opts.flip,
        'tuner': this.ui[this.uiLang].card_effect_opts.tuner,
      }
    },
    cardAttrOpts() {
      return [
        { value: 'DIVINE', text: this.ui[this.uiLang].card_attr_opts.divine },
        { value: 'EARTH', text: this.ui[this.uiLang].card_attr_opts.earth },
        { value: 'WATER', text: this.ui[this.uiLang].card_attr_opts.water },
        { value: 'FIRE', text: this.ui[this.uiLang].card_attr_opts.fire },
        { value: 'WIND', text: this.ui[this.uiLang].card_attr_opts.wind },
        { value: 'LIGHT', text: this.ui[this.uiLang].card_attr_opts.light },
        { value: 'DARK', text: this.ui[this.uiLang].card_attr_opts.dark },
      ]
    },
    cardRaceOpts() {
      return {
        'fiend': this.ui[this.uiLang].card_race_type_opts.fiend,
        'zombie': this.ui[this.uiLang].card_race_type_opts.zombie,
        'sea_serpent': this.ui[this.uiLang].card_race_type_opts.sea_serpent,
        'thunder': this.ui[this.uiLang].card_race_type_opts.thunder,
        'rock': this.ui[this.uiLang].card_race_type_opts.rock,
        'machine': this.ui[this.uiLang].card_race_type_opts.machine,
        'dinosaur': this.ui[this.uiLang].card_race_type_opts.dinosaur,
        'beast': this.ui[this.uiLang].card_race_type_opts.beast,
        'insect': this.ui[this.uiLang].card_race_type_opts.insect,
        'fish': this.ui[this.uiLang].card_race_type_opts.fish,
        'plant': this.ui[this.uiLang].card_race_type_opts.plant,
        'beast_warrior': this.ui[this.uiLang].card_race_type_opts.beast_warrior,
        'warrior': this.ui[this.uiLang].card_race_type_opts.warrior,
        'winged_beast': this.ui[this.uiLang].card_race_type_opts.winged_beast,
        'fairy': this.ui[this.uiLang].card_race_type_opts.fairy,
        'dragon': this.ui[this.uiLang].card_race_type_opts.dragon,
        'reptile': this.ui[this.uiLang].card_race_type_opts.reptile,
        'aqua': this.ui[this.uiLang].card_race_type_opts.aqua,
        'pyro': this.ui[this.uiLang].card_race_type_opts.pyro,
        'spellcaster': this.ui[this.uiLang].card_race_type_opts.spellcaster,
        'wyrm': this.ui[this.uiLang].card_race_type_opts.wyrm,
        'cyberse': this.ui[this.uiLang].card_race_type_opts.cyberse,
        'psychic': this.ui[this.uiLang].card_race_type_opts.psychic,
        'divine_beast': this.ui[this.uiLang].card_race_type_opts.divine_beast,
        'creator_god': this.ui[this.uiLang].card_race_type_opts.creator_god,
      }
    },
    cardTemplateText () {
      let templateUrl = this.cardType!=="Monster"? this.cardType : this.cardSubtype
      if (this.Pendulum && !["Slifer", "Ra", "Obelisk", "LDragon"].includes(this.cardSubtype))
        templateUrl += "Pendulum"
      return templateUrl
    },
    isEffectMonster () {
      return this.cardSubtype==="Effect" || (this.cardEff2!=="none" && this.cardSubtype!=="Normal")
    },
    isXyzMonster () {
      return this.cardType==='Monster' && this.cardSubtype==='Xyz'
    },
    isLinkMonster () {
      return this.cardType==='Monster' && this.cardSubtype==='Link'
    },
    canPendulumEnabled() {
      return this.cardType==='Monster' && !["Slifer", "Ra", "Obelisk", "LDragon"].includes(this.cardSubtype)
    },
    cardEff1Opts () {
      return Object.fromEntries(Object.keys(this.cardEffOpts).filter(key => {
        // 去掉「none」、去掉和Eff2重複的（除了value===normal之外）
        return key !== 'none' && (key === 'normal' || key !== this.cardEff2)
      }).map(key => [key, this.cardEffOpts[key]]))
    },
    cardEff2Opts () {
      return Object.fromEntries(Object.keys(this.cardEffOpts).filter(key => {
        // 去掉和Eff1重複的（除了value===normal之外）
        return key === 'normal' || key !== this.cardEff1
      }).map(key => {
        return [key, (key === 'normal'? this.ui[this.uiLang].m_card.effect: this.cardEffOpts[key])]
      }))
    },
  },
  watch: {
    uiLang() {
      if (this.cardLangOpts[this.uiLang]) this.cardLang = this.uiLang
    },
    cardLang() {
      if (this.cardKey==='') this.load_default_data()
    },
    cardType() {
      this.cardSubtype = 'Normal'
      if (this.cardType!=="Monster") this.Pendulum = false
    },
    cardSubtype() {
      if (["Slifer", "Ra", "Obelisk", "LDragon"].includes(this.cardSubtype)) this.Pendulum = false
    }
  },
  async mounted () {
    try {
      // Initialize language system
      await this.initializeLanguage()

      // Set up event listeners
      window.addEventListener('scroll', this.onScroll)
      window.addEventListener('resize', this.onWindowResize)

      // Sync legacy language with Vuex
      this.syncLanguageWithVuex()

      // Initialize card maker
      this.fireLoadingDialog()
      this.load_default_data()

      // Wait for fonts to load before starting drawing
      this.$nextTick(async () => {
        try {
          // Wait for fonts to load
          if (document.fonts && document.fonts.ready) {
            await document.fonts.ready
          }

          // Initial draw to close loading dialog
          setTimeout(() => {
            this.drawCard()
          }, 500)

          // Start drawing interval
          this.drawCardInterval = setInterval(this.drawCard, 1500)

          // 安全机制：确保加载对话框在5秒后一定会关闭
          setTimeout(() => {
            if (this.loadingDialogShow) {
              this.closeLoadingDialog()
            }
          }, 5000)

        } catch (error) {
          // Fallback: start drawing anyway after a delay and close loading dialog
          setTimeout(() => {
            this.drawCardInterval = setInterval(this.drawCard, 1500)
            this.drawCard()
            this.closeLoadingDialog()
          }, 1000)
        }
      })

    } catch (error) {
      // Emergency fallback
      setTimeout(() => {
        this.closeLoadingDialog()
      }, 2000)
    }
  },

  beforeDestroy () {
    // 清理事件监听器
    window.removeEventListener('scroll', this.onScroll)
    window.removeEventListener('resize', this.onWindowResize)

    // 恢复body滚动
    document.body.style.overflow = ''

    // 清理定时器，防止内存泄漏
    if (this.drawCardInterval) {
      clearInterval(this.drawCardInterval)
      this.drawCardInterval = null
    }
  },

  methods: {
    ...mapMutations(['fireLoadingDialog', 'closeLoadingDialog', 'setLanguage']),
    ...mapActions(['initializeLanguage']),

    // 窗口大小变化处理
    onWindowResize() {
      // 窗口大小变化时的处理逻辑
    },

    // FAQ折叠/展开功能
    toggleFaq(index) {
      // 如果点击的是当前展开的FAQ，则折叠它
      if (this.activeFaqIndex === index) {
        this.activeFaqIndex = null
      } else {
        // 否则展开点击的FAQ（同时折叠其他的）
        this.activeFaqIndex = index
      }
    },

    /**
     * 獲取Open Graph locale格式
     * @param {string} locale - 語言代碼
     * @returns {string} - Open Graph locale格式
     */
    getOgLocale(locale) {
      const localeMap = {
        'en': 'en_US',
        'zh': 'zh_TW',
        'ja': 'ja_JP',
        'de': 'de_DE',
        'fr': 'fr_FR',
        'ko': 'ko_KR',
        'pt': 'pt_PT',
        'es': 'es_ES',
        'el': 'el_GR',
        'th': 'th_TH',
        'ru': 'ru_RU',
        'vi': 'vi_VN'
      }
      return localeMap[locale] || 'en_US'
    },

    /**
     * 語言切換事件處理
     * @param {string} langCode - 新的語言代碼
     */
    onLanguageChanged(langCode) {
      const oldLang = this.uiLang

      // 同步到本地狀態
      this.uiLang = langCode
      this.cardLang = langCode

      // Track language change with Google Analytics
      if (typeof window.trackLanguageChange === 'function') {
        window.trackLanguageChange(langCode, oldLang)
      }

      // 更新所有語言相關的配置選項
      this.updateLanguageOptions()

      // 重新載入預設數據以適應新語言
      this.load_default_data()

      // 重新繪製卡片
      this.doDrawCard()
    },

    /**
     * 更新語言相關的配置選項
     */
    updateLanguageOptions() {
      // 安全檢查並更新卡片稀有度選項
      if (this.cardMeta && this.cardMeta[this.cardLang] && this.cardMeta[this.cardLang].rare) {
        this.cardRareOpts = Object.fromEntries(
          Object.keys(this.cardMeta[this.cardLang].rare).map(key => [
            key,
            this.cardMeta[this.cardLang].rare[key]
          ])
        )
      }

      // 安全檢查並更新其他語言相關的選項
      if (this.cardMeta && this.cardMeta[this.cardLang] && this.cardMeta[this.cardLang].type) {
        this.cardTypeOpts = Object.fromEntries(
          Object.keys(this.cardMeta[this.cardLang].type).map(key => [
            key,
            this.cardMeta[this.cardLang].type[key]
          ])
        )
      }
    },

    /**
     * 同步語言設置到 Vuex 和 i18n
     */
    syncLanguageWithVuex() {
      // 优先使用 i18n 的语言设置
      let targetLang = this.uiLang

      if (this.$i18n && this.$i18n.locale) {
        targetLang = this.$i18n.locale
      } else if (this.currentLanguage !== this.uiLang) {
        this.setLanguage(this.uiLang)
      }

      // 確保卡片語言與 UI 語言同步
      if (this.cardLang !== targetLang) {
        this.cardLang = targetLang
        this.uiLang = targetLang
        this.updateLanguageOptions()
      }
    },

    /**
     * 獲取功能圖標
     * @param {string} iconName - 圖標名稱
     * @returns {Array} Font Awesome 圖標數組
     */
    getFeatureIcon(iconName) {
      const iconMap = {
        cards: ['fas', 'layer-group'],
        globe: ['fas', 'globe'],
        download: ['fas', 'download'],
        eye: ['fas', 'eye'],
        magic: ['fas', 'magic'],
        heart: ['fas', 'heart']
      }

      return iconMap[iconName] || ['fas', 'star']
    },

    /**
     * 獲取原因圖標
     * @param {string} iconName - 圖標名稱
     * @returns {Array} Font Awesome 圖標數組
     */
    getReasonIcon(iconName) {
      const iconMap = {
        star: ['fas', 'star'],
        'user-friendly': ['fas', 'user-check'],
        gift: ['fas', 'gift'],
        unlock: ['fas', 'unlock-alt'],
        globe: ['fas', 'globe'],
        image: ['fas', 'image']
      }
      return iconMap[iconName] || ['fas', 'check']
    },

    /**
     * 獲取步驟圖標
     * @param {string} iconName - 圖標名稱
     * @returns {Array} Font Awesome 圖標數組
     */
    getStepIcon(iconName) {
      const iconMap = {
        list: ['fas', 'list'],
        upload: ['fas', 'upload'],
        edit: ['fas', 'edit'],
        download: ['fas', 'download']
      }
      return iconMap[iconName] || ['fas', 'cog']
    },

    /**
     * 重置卡片旋轉視角
     */
    resetCardRotation() {
      const cardDisplay = this.$refs['yugiohcard-wrap']
      if (cardDisplay) {
        cardDisplay.style.transform = ''
      }
    },

    /**
     * 切換全屏預覽
     */
    toggleFullscreen() {
      const canvas = this.$refs.yugiohcard
      if (canvas) {
        if (document.fullscreenElement) {
          document.exitFullscreen()
        } else {
          canvas.requestFullscreen().catch(err => {
            console.log('無法進入全屏模式:', err)
          })
        }
      }
    },

    /**
     * 複製卡片圖片到剪貼板
     */
    copyCardImage() {
      try {
        const canvas = this.$refs.yugiohcard
        if (canvas) {
          canvas.toBlob(async (blob) => {
            if (blob) {
              try {
                if (typeof ClipboardItem !== 'undefined') {
                  await navigator.clipboard.write([
                    new ClipboardItem({ 'image/png': blob })
                  ])
                } else {
                  throw new TypeError('ClipboardItem not supported')
                }
                // 可以添加成功提示
                console.log('卡片圖片已複製到剪貼板')
              } catch (clipboardErr) {
                console.log('剪貼板操作失敗:', clipboardErr)
                // 降級方案：下載圖片
                this.download_img()
              }
            }
          })
        }
      } catch (err) {
        console.log('複製失敗:', err)
        // 降級方案：下載圖片
        this.download_img()
      }
    },

    doDrawCard() {
      // Only show loading dialog for manual generation
      this.fireLoadingDialog()
      this.drawCard()
    },

    // 卡片繪製 - 繪製前準備
    async drawCard () {
      try {
        let cardImgUrl = this.cardImg? URL.createObjectURL(this.cardImg): null

        // 安全检查 cardMeta
        if (!this.cardMeta || !this.cardMeta[this.cardLang]) {
          this.closeLoadingDialog()
          return
        }

        const templateLang = this.cardMeta[this.cardLang]._templateLang
        // 检查模板语言是否有对应的模板文件，如果没有则回退到英文
        const availableTemplateLangs = ['en', 'ja', 'zh']
        const finalTemplateLang = availableTemplateLangs.includes(templateLang) ? templateLang : 'en'

        if (this.cardLoadYgoProEnabled) {
          try {
            const hasData = await this.load_ygopro_data(this.cardKey);
            if (hasData) cardImgUrl = `/ygo/pics/${this.cardKey}.jpg`
          } catch (error) {
            console.warn('Failed to load YGO Pro data:', error)
          }
        }
      this.imgs = {
        template: `/images/card/${finalTemplateLang}/${this.cardTemplateText}.png`,
        holo: "/images/pic/holo.png",
        link1: "/images/pic/LINK1.png", link2: "/images/pic/LINK2.png",
        link3: "/images/pic/LINK3.png", link4: "/images/pic/LINK4.png",
        link6: "/images/pic/LINK6.png", link7: "/images/pic/LINK7.png",
        link8: "/images/pic/LINK8.png", link9: "/images/pic/LINK9.png",
        attr: (
          this.cardType==="Monster" ?
          `/images/attr/${finalTemplateLang}/${this.cardAttr}.webp` :
          `/images/attr/${finalTemplateLang}/${this.cardType}.webp`
        ),
        photo: cardImgUrl || "/images/default.jpg",
        levelOrSubtype: (
          this.cardType!=="Monster" && this.cardSubtype!=="Normal" ?
          `/images/pic/${this.cardSubtype}.webp` :
          `/images/pic/${this.isXyzMonster ? 'Rank' : 'Level'}.webp`
        ),
      }
      this.drawCardLoadingImages(this.drawCardProcess) // 載入卡圖後，繪製卡片內容
      } catch (error) {
        this.closeLoadingDialog()
      }
    },

    // 卡片繪製 - 載入卡圖
    drawCardLoadingImages (callback) {
      const length = Object.keys(this.imgs).length
      let count = 0
      for (const key in this.imgs) {
        const image = new window.Image()
        image.src = this.imgs[key]
        this.imgs[key] = image
        this.imgs[key].onload = () => {
          count += 1
          if (count >= length) {
            setTimeout(() => {
              callback()
              // 当所有图片加载完成且Canvas绘制成功后，关闭加载对话框
              this.closeLoadingDialog()
            }, 200)
          }
        }
        this.imgs[key].onerror = () => {
          count += 1
          if (count >= length) {
            setTimeout(() => {
              callback()
              this.closeLoadingDialog()
            }, 200)
          }
        }
      }
    },
    
    // 卡片繪製 - 主要繪製流程
    drawCardProcess () {
      const canvas = this.$refs.yugiohcard
      if (!canvas) {
        console.warn('Canvas element not ready yet')
        return
      }
      const ctx = canvas.getContext('2d')
      // Increase canvas resolution for better quality
      const scale = 2 // Higher resolution multiplier
      canvas.width = 1000 * scale
      canvas.height = 1450 * scale

      // Scale the context to maintain proper drawing coordinates
      ctx.scale(scale, scale)

      // Improve rendering quality
      ctx.imageSmoothingEnabled = true
      ctx.imageSmoothingQuality = 'high'

      // 优化文本渲染质量
      ctx.textRenderingOptimization = 'optimizeQuality'
      ctx.fontKerning = 'normal'

      const langStr = this.cardMeta[this.cardLang]
      const offset = langStr._offset
      const fontName = langStr._fontName

      // 繪製底圖
      this.drawCardImg(ctx)

      // 卡片標題
      this.drawCardTitle(ctx, offset, fontName)

      // 卡片資料
      this.drawCardInfo(ctx, langStr, offset, fontName)
      
      // 卡片密碼 - Hidden
      // Card password display is disabled
      /*
      if (this.cardKey!=="") {
        ctx.fillStyle = (this.isXyzMonster && !this.Pendulum )? '#FFF': '#000'
        ctx.font = `22pt 'cardkey', 'MatrixBoldSmallCaps', ${fontName[2]}`
        ctx.textAlign = "left";
        ctx.fillText(this.cardKey.padStart(8, '0'), 54, 1405); // 卡片密碼
      }
      */
      ctx.fillStyle = '#000';

      // 防偽貼            
      if (this.holo) ctx.drawImage(this.imgs.holo, 928, 1371, 44, 46);

      // 靈擺效果說明
      if (this.Pendulum) this.drawCardPendulumInfoText(ctx, offset, fontName);

      // 卡片說明
      this.drawCardInfoText(ctx, offset, fontName);

      // 移除重复的 closeLoadingDialog 调用，因为 drawCardLoadingImages 已经处理了
    },

    // 主要繪製流程 - 底圖
    drawCardImg (ctx) {
      let cX, cY, cW, cH;
      if (this.Pendulum) { cX=69; cY=255; cW=862; cH=647; }
      else { cX=123; cY=268; cW=754; cH=754; }

      const photo = this.imgs.photo
      const iW = photo.width / photo.height * cH
      const iH = photo.height / photo.width * cW
      if (photo.width <= photo.height*(this.Pendulum? 1.33:1)) ctx.drawImage(photo, cX, cY-((iH-cH)/2), cW, iH);
      else ctx.drawImage(photo, cX-((iW-cW)/2), cY, iW, cH);
      ctx.drawImage(this.imgs.template, 0, 0, 1000, 1450);
      ctx.drawImage(this.imgs.attr, 840, 68, 90, 90);
    },

    // 主要繪製流程 - 標題
    drawCardTitle (ctx, offset, fontName) {
      // 优化标题字体大小，基础大小调整为52pt以获得更好的视觉效果
      const titleFontSize = 52 + offset.tS;

      // 根据语言调整字体权重和渲染设置
      const fontWeight = this.cardLang === 'zh' ? 'bold' :
                        this.cardLang === 'ja' ? '600' : 'normal';

      // 优化字体渲染质量
      ctx.textRenderingOptimization = 'optimizeQuality';
      ctx.fontKerning = 'normal';

      // 构建字体栈，确保中日文字体优先级
      let fontStack = '';
      if (this.cardLang === 'zh') {
        fontStack = `${fontName[0]}, ${fontName[3]}, "Noto Sans SC", "Microsoft YaHei", ${fontName[4]}, ${fontName[5]}`;
      } else if (this.cardLang === 'ja') {
        fontStack = `${fontName[0]}, ${fontName[1]}, "Noto Sans JP", "Hiragino Sans", ${fontName[3]}, ${fontName[4]}, ${fontName[5]}`;
      } else {
        fontStack = `${fontName[0]}, ${fontName[3]}, ${fontName[4]}, ${fontName[5]}`;
      }

      ctx.font = `${fontWeight} ${titleFontSize}pt ${fontStack}`;
      ctx.fillStyle = this.rareColor(ctx);

      // 添加文本阴影以提高可读性
      ctx.shadowColor = "rgba(0, 0, 0, 0.3)";
      ctx.shadowBlur = 2;
      ctx.shadowOffsetX = 1;
      ctx.shadowOffsetY = 1;

      ctx.fillText(this.cardTitle, 77+offset.tX, 140+offset.tY, 750);

      // 重置阴影
      ctx.shadowColor = "transparent";
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
    },

    // 主要繪製流程 - 內容
    drawCardInfo (ctx, langStr, offset, fontName) {
      const linkPosition = { 
        Link: {
          X: [86,410,826,70, 0, 878,86,410,826], Y: [231,214,231,556, 0, 556,967,1020,967],
          W: [86,177,86,52, 0, 52,86,177,86], H: [86,52,86,177, 0, 177,86,52,86]
        },
        LinkPendulum: {
          X: [42,421,881,21, 0, 934,42,421,881], Y: [227,211,227,732, 0, 732,1319,1365,1319],
          W: [75,155,75,46, 0, 46,75,155,75], H: [75,46,75,155, 0, 155,75,46,75]
        }
      }
      ctx.font = `${(this.cardType==="Monster" ? 25 : 40) - offset.sS}pt ${fontName[1]}`
      ctx.fillStyle = '#000';
      if (this.cardType==="Monster") { // 怪獸卡
        // 怪獸屬性文字
        const cardSubtypeFilter = ["Normal", "Effect", "Slifer", "Ra", "Obelisk", "LDragon"]
        const typeText = (this.cardCustomRaceEnabled? this.cardCustomRace : langStr.Race[this.cardRace]) +        // 種族
                         (this.Special? langStr.M_SPECIAL: "") +                                                  // 特殊召喚
                         (!cardSubtypeFilter.includes(this.cardSubtype)? langStr.Subtype[this.cardSubtype]: "") + // 卡面種類
                         (langStr.Effect[this.cardEff1]) +                                                        // 功能1(效果)
                         (this.cardEff1!==this.cardEff2? langStr.Effect[this.cardEff2]: "") +                     // 功能2(效果)
                         (this.Pendulum? langStr.M_PENDULUM: "") +                                                // 功能3(靈擺有無)
                         (this.isEffectMonster? langStr.M_EFFECT: "")                                             // 功能4(效果有無)
        
        // 怪獸屬性
        ctx.fillText(`${langStr.QUOTE_L}${typeText}${langStr.QUOTE_R}`, 63 + offset.oX, 1120 + offset.oY, 750);

        // 怪獸ATK
        ctx.font = `33pt 'MatrixBoldSmallCaps', ${fontName[2]}`
        ctx.textAlign = "right";
        if (this.cardATK.includes("∞")) {
          ctx.font = `Bold 32pt 'Times New Roman', ${fontName[2]}`
        }
        ctx.fillText(this.cardATK, 719, 1353, 95)

        // 怪獸DEF / LINK
        ctx.font = `33pt 'MatrixBoldSmallCaps', ${fontName[2]}`
        if (this.isLinkMonster) {
          this.cardDEF = String(Object.values(this.links).filter(item => item.val).length)
          ctx.font = `28pt 'link', 'MatrixBoldSmallCaps', ${fontName[2]}`
        } else if (this.cardDEF.includes("∞")) {
          ctx.font = `Bold 32pt 'Times New Roman', ${fontName[2]}`
        }
        ctx.fillText(this.cardDEF, 920 - (this.isLinkMonster? 3: 0), 1353 - (this.isLinkMonster? 1: 0), 95);

        // 怪獸等級 / 階級 / 連結
        ctx.textAlign = "left";
        if (!this.isLinkMonster) { // 非連結怪獸
          for(let i=1; i<=this.cardLevel; i++)
            ctx.drawImage(this.imgs.levelOrSubtype, (this.isXyzMonster? (122+(i-1)*63): (820-(i-1)*63)), 181, 58, 58);
        } else {                   // 連結怪獸
          const linkStr = this.Pendulum? "LinkPendulum": "Link";
          // 連結圖片
          for(let i=1; i<=9; i++)
            if(i!==5 && this.links[i].val)
              ctx.drawImage(this.imgs[`link${i}`], 
                linkPosition[linkStr].X[i-1], linkPosition[linkStr].Y[i-1], 
                linkPosition[linkStr].W[i-1], linkPosition[linkStr].H[i-1]
              );
        }
      } else {                         // 魔罠卡
        // 卡種
        const typeText = (this.cardType==="Spell"? langStr.Spell: langStr.Trap) + (this.cardSubtype==='Normal'? "": langStr.SEP)
        ctx.textAlign = "right";
        ctx.fillText(`${langStr.QUOTE_L}${typeText}${langStr.QUOTE_R}`, 920+offset.sX1, 222+offset.sY1); // 魔罠卡
        if (this.cardSubtype!=='Normal')
          ctx.drawImage(this.imgs.levelOrSubtype, 820+offset.sX2, 178+offset.sY2, 58, 58);        // 魔罠子類別
      }
    },

    // 填入靈擺效果說明
    drawCardPendulumInfoText (ctx, offset, fontName){
      // 畫符號 - 优化数字字体渲染
      ctx.textAlign = "center";
      ctx.font = "55pt 'MatrixBoldSmallCaps'";

      // 添加数字阴影效果
      ctx.shadowColor = "rgba(0, 0, 0, 0.2)";
      ctx.shadowBlur = 1;
      ctx.shadowOffsetX = 1;
      ctx.shadowOffsetY = 1;

      ctx.fillText(this.cardBLUE, 106-((['Xyz', 'Link', 'Token'].includes(this.cardSubtype) || this.cardType!=="Monster")? 5: 0 ), 1040, 60);
      ctx.fillText(this.cardRED, 895, 1040, 60);

      // 重置阴影
      ctx.shadowColor = "transparent";
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;

      // 畫文字 - 优化灵摆效果文本
      const fontSize = Number(this.pendulumSize)
      ctx.textAlign = "left";
      ctx.textBaseline = "top";

      // 优化字体渲染质量
      ctx.textRenderingOptimization = 'optimizeQuality';
      ctx.fontKerning = 'normal';

      // 根据语言调整字体权重和字体栈
      const fontWeight = this.cardLang === 'zh' ? '500' :
                        this.cardLang === 'ja' ? '500' : 'normal';

      // 构建字体栈，确保中日文字体优先级
      let fontStack = '';
      if (this.cardLang === 'zh') {
        fontStack = `${fontName[2]}, ${fontName[3]}, "Noto Sans SC", "Microsoft YaHei", ${fontName[4]}, ${fontName[5]}`;
      } else if (this.cardLang === 'ja') {
        fontStack = `${fontName[2]}, ${fontName[1]}, "Noto Sans JP", "Hiragino Sans", ${fontName[3]}, ${fontName[4]}, ${fontName[5]}`;
      } else {
        fontStack = `${fontName[2]}, ${fontName[3]}, ${fontName[4]}, ${fontName[5]}`;
      }

      ctx.font = `${fontWeight} ${fontSize}pt ${fontStack}`;

      // 优化行间距计算
      const lineHeight = fontSize + Math.max(offset.lh, 5); // 灵摆文本稍大的行间距
      this.wrapText(ctx, this.cardPendulumInfo, 160, 920+offset.oY, 660, lineHeight);
    },

    // 填入卡片說明
    drawCardInfoText (ctx, offset, fontName) {
      const fontSize = Number(this.infoSize)
      ctx.textAlign = "left";
      ctx.textBaseline = "top";

      // 优化字体渲染质量
      ctx.textRenderingOptimization = 'optimizeQuality';
      ctx.fontKerning = 'normal';

      // 根据语言调整字体权重和字体栈
      const fontWeight = this.cardLang === 'zh' ? '500' :
                        this.cardLang === 'ja' ? '500' : 'normal';

      // 构建字体栈，确保中日文字体优先级
      let fontStack = '';
      if (this.cardLang === 'zh') {
        fontStack = `${fontName[2]}, ${fontName[3]}, "Noto Sans SC", "Microsoft YaHei", ${fontName[4]}, ${fontName[5]}`;
      } else if (this.cardLang === 'ja') {
        fontStack = `${fontName[2]}, ${fontName[1]}, "Noto Sans JP", "Hiragino Sans", ${fontName[3]}, ${fontName[4]}, ${fontName[5]}`;
      } else {
        fontStack = `${fontName[2]}, ${fontName[3]}, ${fontName[4]}, ${fontName[5]}`;
      }

      ctx.font = `${fontWeight} ${fontSize}pt ${fontStack}`;

      // 优化行间距计算，确保与参考网站一致
      const lineHeight = fontSize + Math.max(offset.lh, 4); // 最小行间距为4pt
      this.wrapText(ctx, this.cardInfo, 75, 1095+offset.oY+(this.cardType==="Monster"? 30: 0), 825, lineHeight)
    },

    // 卡色
    rareColor (ctx) {
      let gradient
      switch(this.cardRare) {
        case "2":
          ctx.shadowColor = "#dcff32";
          ctx.shadowBlur = 1;
          ctx.shadowOffsetX = 0.4;
          ctx.shadowOffsetY = 1.5;
          return "#524100"; // "#3b2f00";
        case "1":
          gradient = ctx.createLinearGradient(0, 0, 600, 0);
          gradient.addColorStop("0", "#ffdabf");
          gradient.addColorStop("0.14", "#fff6bf");
          gradient.addColorStop("0.28", "#fffebf");
          gradient.addColorStop("0.42", "#d8ffbf");
          gradient.addColorStop("0.56", "#bfffd4");
          gradient.addColorStop("0.7", "#bffdff");
          gradient.addColorStop("0.84", "#bfe4ff");
          gradient.addColorStop("1", "#bfc2ff");
          return gradient;
        default:
          return this.titleColor;
      }
    },

    // 文字區
    wrapText (ctx, text, x, y, maxWidth, lineHeight) {
      let lineWidth = 0 - ctx.measureText(text[0]).width; // 目前佔用行寬
      const fieldWidth = maxWidth;                        // 欄位寬度
      let initHeight = y;                                 // 文字距離圖片頂部高度
      let lastSubStrIndex = 0;                            // 每次擷取的子字串起始位置
      for(let i=0; i<text.length; i++){ 
        lineWidth += ctx.measureText(text[i]).width;
        if(lineWidth>fieldWidth || text.substring(i, i+1)==='\n') {
          if(text.substring(i, i+1)==='\n') i++;
          ctx.fillText(text.substring(lastSubStrIndex, i), x, initHeight);
          initHeight+=lineHeight
          lineWidth=0
          lastSubStrIndex=i;
        } 
        if(i===text.length-1){   // 若本行未超過，位已達最後一字，則直接填入
          ctx.fillText(text.substring(lastSubStrIndex, i+1), x, initHeight);
        }
      }
    },

    // 下載
    download_img () {
      const canvas = this.$refs.yugiohcard
      if (canvas.msToBlob) { // for IE
        const blob = canvas.msToBlob();
        window.navigator.msSaveBlob(blob, 'YuGiOh.png');
      } else {
        const a = document.createElement('a');
        a.href = canvas.toDataURL("image/jpeg");
        a.download = 'YuGiOh.jpg';
        a.click();
      }

      // Track card download event with Google Analytics
      if (typeof window.trackCardDownload === 'function') {
        window.trackCardDownload('jpg')
      }
    },

    // 載入預設
    load_default_data () {
      try {
        const data = this.cardMeta[this.cardLang] && this.cardMeta[this.cardLang].Default
        if (!data) {
          return
        }
      this.holo = true
      this.cardRare = "0"
      this.titleColor = "#000000"
      this.cardLoadYgoProEnabled = true
      this.cardKey = ""
      this.cardTitle = data.title
      this.cardImg = null
      this.cardType = "Monster"
      this.cardSubtype = "Normal"
      this.cardAttr = "LIGHT"
      this.cardEff1 = "normal"
      this.cardEff2 = "none"
      this.cardCustomRaceEnabled = false
      this.cardCustomRace = ""
      this.cardRace = "dragon"
      this.Pendulum = true
      this.Special = true
      this.cardLevel = "12"
      this.cardBLUE = "12"
      this.cardRED = "12"
      this.cardATK = "?"
      this.cardDEF = "?"
      for (let i=1; i<=9; i++)
        if (i!==5)
          this.links[i].val = false
      this.cardInfo = data.info
      this.infoSize = data.size
      this.cardPendulumInfo = data.pInfo
      this.pendulumSize = data.pSize
      } catch (error) {
        // 设置基本的默认值
        this.cardTitle = 'Sample Card'
        this.cardInfo = 'Sample card description'
        this.infoSize = 20 // 优化默认字体大小
        this.cardPendulumInfo = 'Sample pendulum effect'
        this.pendulumSize = 22 // 优化默认pendulum字体大小
      }
    },

    // 載入YGOPRO2資料
    async load_ygopro_data(key) {
      // 确保数据已加载
      await this.loadYgoproData()

      const data = this.ygoproData && this.ygoproData[key]
      if (!data)
        return false
      this.cardLang = "zh"
      this.cardRare = data.rare
      this.titleColor = data.color
      this.cardTitle = data.title
      this.cardImg = null //
      this.cardType = data.type[0]
      this.cardSubtype = data.type[1]
      if (data.attribute!=="Trap" && data.attribute!=="Spell")
        this.cardAttr = data.attribute
      this.cardEff1 = data.type[2]
      this.cardEff2 = data.type[3]
      this.cardCustomRaceEnabled = false
      this.cardCustomRace = ""
      this.cardRace = data.race
      this.Pendulum = data.type[4]
      this.Special = data.type[5]
      this.cardLevel = data.level
      this.cardBLUE = data.blue
      this.cardRED = data.red
      this.cardATK = data.atk
      this.cardDEF = data.def
      for (let i=1; i<=9; i++)
        if (i!==5)
          this.links[i].val = data[`link${i}`]
      this.cardInfo = data.infoText
      this.infoSize = data.size
      this.cardPendulumInfo = data.pendulumText
      this.pendulumSize = data.pSize
      return true
    },
    
    // 頁面捲動時
    onScroll () {
      const currentScrollPosition = window.pageYOffset || document.documentElement.scrollTop
      this.pageScrolling = currentScrollPosition
    },

    /**
     * 异步加载YGO数据
     */
    async loadYgoproData() {
      if (this.ygoproDataLoaded || this.ygoproDataLoading) {
        return this.ygoproData
      }

      this.ygoproDataLoading = true

      try {
        // 优先尝试加载压缩版本
        const response = await fetch('/ygo/card_data.json.gz')
        if (response.ok) {
          // 如果支持压缩，使用压缩版本
          const arrayBuffer = await response.arrayBuffer()
          const decompressed = new TextDecoder().decode(arrayBuffer)
          this.ygoproData = JSON.parse(decompressed)
        } else {
          // 回退到普通版本
          const fallbackResponse = await fetch('/ygo/card_data.json')
          this.ygoproData = await fallbackResponse.json()
        }

        this.ygoproDataLoaded = true
        return this.ygoproData
      } catch (error) {
        console.warn('Failed to load YGO data:', error)
        this.ygoproData = {}
        return this.ygoproData
      } finally {
        this.ygoproDataLoading = false
      }
    },

    // 3D效果 - 移動
    move(e) {
      const THRESHOLD = 5;
      const cardWrap = this.$refs["yugiohcard-wrap"];
      const { clientX, clientY, currentTarget } = e;
      const { clientWidth, clientHeight, offsetLeft, offsetTop } = currentTarget;

      const horizontal = (clientX - offsetLeft) / clientWidth;
      const vertical = (clientY - offsetTop) / clientHeight;

      const rotateX = (THRESHOLD / 2 - horizontal * THRESHOLD).toFixed(2);
      const rotateY = (vertical * THRESHOLD - THRESHOLD / 2).toFixed(2);

      cardWrap.style.transform = `perspective(${clientWidth}px) rotateX(${rotateY}deg) rotateY(${rotateX}deg) scale3d(1, 1, 1)`;
    },

    // 3D效果 - 離開
    leave(e) {
      const cardWrap = this.$refs["yugiohcard-wrap"];
      cardWrap.style.transform = `perspective(${e.currentTarget.clientWidth}px) rotateX(0deg) rotateY(0deg)`;
    },
  }
}
</script>

<style>
/* font converted using font-converter.net. thank you! */

.preloadfont {
	font-family: YourFont;
	opacity:0;
	height:0;
	width:0;
	display:inline-block;
}

:root {
  --chevron-down-svg-path: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3Cpath fill='%23CCC' d='M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z'%3E%3C/path%3E%3C/svg%3E")
}

body{
  background: url("/Screentone.png") round,
              -webkit-linear-gradient(to bottom right, #000000BB, #66666699, #000000BB),
              -webkit-linear-gradient(to bottom left, #111111BB, #11111199, #111111BB);
  background: url("/Screentone.png") round,
              -moz-linear-gradient(to bottom right, #000000BB, #66666699, #000000BB),
              -moz-linear-gradient(to bottom left, #111111BB, #11111199, #111111BB);
  background: url("/Screentone.png") round,
              -o-linear-gradient(to bottom right, #000000BB, #66666699, #000000BB),
              -o-linear-gradient(to bottom left, #111111BB, #11111199, #111111BB);
  background: url("/Screentone.png") round,
              linear-gradient(to bottom right, #000000BB, #66666699, #000000BB),
              linear-gradient(to bottom left, #111111BB, #11111199, #111111BB);
  background-blend-mode: multiply;
	font-family: 'Noto Sans JP', 'Noto Sans TC', 'Noto Sans SC', 'arial', "微軟正黑體";
}

/* -------------------- Yu-Gi-Oh! Theme Colors -------------------- */
:root {
  --bg-primary: #0d1421;
  --bg-secondary: #1a2332;
  --bg-tertiary: #2a3441;
  --bg-card: #1f2937;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --accent-primary: #fbbf24;
  --accent-secondary: #8b5cf6;
  --accent-tertiary: #06b6d4;
  --accent-danger: #ef4444;
  --yugioh-gold: #fbbf24;
  --yugioh-purple: #8b5cf6;
  --yugioh-blue: #3b82f6;
  --yugioh-dark-blue: #1e40af;
  --border-color: #374151;
  --shadow-light: rgba(251, 191, 36, 0.1);
  --shadow-dark: rgba(0, 0, 0, 0.6);
  --gradient-primary: linear-gradient(135deg, var(--yugioh-gold), var(--yugioh-purple));
  --gradient-secondary: linear-gradient(135deg, var(--yugioh-blue), var(--yugioh-dark-blue));
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* -------------------- Header Styles -------------------- */
.main-navbar {
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 4px 20px var(--shadow-dark);
  transition: all 0.3s ease;
  min-height: 70px;
  padding: 0.5rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 右侧控制区域 - 桌面端默认样式 */
.navbar-right-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}



.navbar-brand-custom {
  text-decoration: none;
  transition: transform 0.2s ease;
}

.navbar-brand-custom:hover {
  transform: translateY(-1px);
}

.brand-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.brand-logo {
  height: 60px;
  width: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.brand-text {
  display: flex;
  flex-direction: column;
}

.brand-title {
  font-family: 'Inter', sans-serif;
  color: var(--text-primary);
  font-weight: 700;
}

.brand-main {
  font-size: 1.4rem;
  display: block;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.brand-sub {
  font-size: 0.8rem;
  opacity: 0.7;
  font-weight: 400;
  color: var(--text-secondary);
}

.nav-link-custom {
  color: var(--text-secondary) !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  border-radius: 8px;
  transition: all 0.2s ease;
  margin: 0 0.25rem;
}

.nav-link-custom:hover {
  color: var(--text-primary) !important;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

/* -------------------- Card Editor Section -------------------- */
.card-editor-section {
  background: var(--bg-primary);
  padding: 2rem 0;
  margin-top: 80px;
}

/* 确保主要内容区域的居中布局 */
.card-editor-section .container-fluid {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-editor-section .row {
  width: 100%;
  max-width: 1200px;
}

.editor-panel {
  background: var(--bg-secondary);
  padding: 0;
  overflow-y: auto;
  min-height: 85vh;
  max-height: 85vh;
  border-radius: 16px;
  border: 1px solid var(--border-color);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  position: sticky;
  top: 80px;
}

.editor-container {
  padding: 0; /* 移除内边距，由子元素控制 */
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 可滚动的内容区域 */
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  padding-bottom: 0.5rem; /* 为固定底部留出空间 */
}

/* 固定底部控制区域 */
.fixed-bottom-controls {
  position: sticky;
  bottom: 0;
  background: var(--bg-secondary);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem;
  backdrop-filter: blur(10px);
  z-index: 10;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3); /* 添加阴影提高层次感 */
}

/* 移动端固定底部控制区域优化 */
@media (max-width: 768px) {
  .fixed-bottom-controls {
    padding: 1rem 0.75rem; /* 调整移动端内边距 */
    border-top-width: 2px; /* 增加边框宽度 */
  }
}

@media (max-width: 480px) {
  .fixed-bottom-controls {
    padding: 0.75rem 0.5rem; /* 超小屏幕减少内边距 */
  }
}



/* -------------------- Ad Spaces -------------------- */
.ad-space {
  min-height: 85vh;
  position: sticky;
  top: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  border: 2px dashed var(--border-color);
  border-radius: 12px;
  color: var(--text-muted);
  font-size: 0.8rem;
  text-align: center;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.ad-space:hover {
  opacity: 0.8;
  border-color: var(--yugioh-gold);
}

.left-ad {
  margin-right: 1rem;
}

.right-ad {
  margin-left: 1rem;
}

.preview-panel {
  background: var(--bg-primary);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 85vh;
  max-height: 85vh;
  position: sticky;
  top: 80px;
}

.preview-container {
  width: 100%;
  max-width: 480px; /* Increased from 380px for better balance */
  text-align: center;
}

.card-preview-wrapper {
  background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
  border-radius: 28px;
  padding: 3.5rem 2.5rem; /* 增加内边距，使黄色边框比卡片内容区域更大 */
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 var(--shadow-light),
    0 0 0 2px var(--yugioh-gold); /* 增加边框宽度从1px到2px */
  border: 3px solid transparent; /* 增加透明边框宽度 */
  background-clip: padding-box;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  /* 添加内部阴影增强层次感 */
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 var(--shadow-light),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1),
    0 0 0 2px var(--yugioh-gold),
    0 0 10px rgba(251, 191, 36, 0.2);
}

.card-preview-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    var(--shadow-light),
    transparent
  );
  transition: left 0.6s;
}

.card-preview-wrapper:hover::before {
  left: 100%;
}

.card-preview-wrapper:hover {
  transform: translateY(-10px) scale(1.03); /* 增加悬停效果的强度 */
  box-shadow:
    0 35px 90px rgba(0, 0, 0, 0.7),
    inset 0 1px 0 var(--shadow-light),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15),
    0 0 0 3px var(--yugioh-gold), /* 悬停时边框更粗 */
    0 0 25px rgba(251, 191, 36, 0.4), /* 增强发光效果 */
    0 0 50px rgba(251, 191, 36, 0.2); /* 添加外层发光 */
}

.card-display {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
  will-change: transform;
  cursor: pointer;
  /* 确保卡片内容与边框有合适的间距 */
  margin: 0.5rem 0;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  /* 添加内部容器的背景和阴影 */
  background: rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  padding: 0.75rem;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 卡片画布样式优化 */
.card-canvas {
  border-radius: 12px;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  max-width: 100%;
  height: auto;
  display: block;
}

.card-canvas:hover {
  box-shadow:
    0 12px 35px rgba(0, 0, 0, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.3);
  transform: scale(1.01);
}

.card-canvas {
  max-width: 100%;
  height: auto;
  border-radius: 16px;
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.6),
    0 5px 15px rgba(0, 0, 0, 0.4);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid rgba(255, 255, 255, 0.1);
  /* Improve canvas rendering quality */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: pixelated;
  /* Reset image-rendering for high-DPI displays */
  image-rendering: auto;
}

.card-display:hover {
  transform: perspective(1000px) rotateX(5deg) rotateY(10deg);
}

.card-display:hover .card-canvas {
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.8),
    0 10px 25px rgba(0, 0, 0, 0.6);
  border-color: rgba(255, 255, 255, 0.2);
}



.preview-info {
  margin-top: 1rem;
}

.form-section {
  border: none;
  margin-bottom: 2rem;
}

.form-section-title {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-color);
}

label {
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.5rem; /* 增加底部间距 */
  font-size: 0.9rem; /* 增大字体提高可读性 */
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4; /* 增加行高 */
  min-height: 20px; /* 确保标签有足够高度 */
}

/* -------------------- Custom Form Controls -------------------- */
.form-check-wrapper {
  margin-top: 0.5rem;
}

.custom-checkbox {
  width: 100%;
}

.custom-checkbox .btn {
  width: 100%;
  text-align: left;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.color-input-wrapper {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.color-input {
  width: 60px;
  height: 40px;
  padding: 0;
  border-radius: 8px;
  cursor: pointer;
}

.color-value {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.form-text {
  font-size: 0.8rem;
  color: var(--text-muted);
  display: flex;
  align-items: center;
}

/* -------------------- Form Controls -------------------- */
.form-control,
.form-select {
  background: var(--bg-tertiary);
  border: 1px solid rgba(55, 65, 81, 0.3);
  color: var(--text-primary);
  border-radius: 12px;
  padding: 0.75rem 1rem; /* 增加内边距，提高触摸友好性 */
  font-size: 0.9rem; /* 稍微增大字体 */
  transition: all 0.3s ease;
  font-weight: 400;
  height: auto;
  min-height: 38px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 44px; /* 确保最小触摸目标大小 */
  -webkit-appearance: none; /* 移除iOS默认样式 */
  -moz-appearance: none; /* 移除Firefox默认样式 */
  appearance: none; /* 标准属性 */
}

.form-control:focus,
.form-select:focus {
  background: var(--bg-card);
  border-color: var(--yugioh-gold);
  color: var(--text-primary);
  box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.15), 0 4px 12px rgba(0, 0, 0, 0.15);
  outline: none;
  transform: translateY(-1px);
}

.form-control::placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

/* Form rows styling - Simplified design */
.row.my-3 {
  margin: 1rem 0;
  padding: 0;
  background: transparent;
  border: none;
  border-radius: 0;
  transition: none;
}

.row.my-3:hover {
  border-color: transparent;
  box-shadow: none;
}

/* Form group styling for better organization */
.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Column spacing optimization */
.px-2 {
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}

/* Button group styling */
.button-group {
  gap: 0.75rem !important;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.button-group .btn {
  min-width: 130px; /* 增加最小宽度 */
  font-weight: 500;
  flex: 0 0 auto; /* 防止按钮被压缩 */
}

/* Responsive label optimization */
@media (max-width: 576px) {
  label {
    font-size: 0.85rem; /* 从0.8rem增加到0.85rem，提高可读性 */
    margin-bottom: 0.375rem; /* 从0.25rem增加到0.375rem，增加间距 */
    line-height: 1.3;
    white-space: normal; /* 允许换行，避免文字被截断 */
    overflow: visible;
    text-overflow: initial;
  }

  .px-2 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
    margin-bottom: 1rem;
  }

  .button-group {
    justify-content: center !important;
    gap: 0.5rem !important;
  }

  .button-group .btn {
    min-width: 110px; /* 增加最小宽度 */
    font-size: 0.875rem;
    padding: 0.75rem 1rem; /* 增加内边距，提高触摸友好性 */
    min-height: 48px; /* 确保移动端触摸目标大小 */
    border-radius: 8px; /* 适当减小圆角适应小屏幕 */
  }
}

/* Improved form layout */
.card-settings-form {
  padding: 0;
}

.card-settings-form .row {
  margin-left: -0.75rem;
  margin-right: -0.75rem;
}

/* Color input styling */
input[type="color"] {
  width: 100%; /* 在移动端占满容器宽度 */
  min-width: 60px; /* 最小宽度 */
  height: 44px; /* 增加高度，提高触摸友好性 */
  padding: 0;
  border: 1px solid var(--border-color);
  border-radius: 10px; /* 增加圆角 */
  cursor: pointer;
  background: var(--bg-tertiary);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* 移动端颜色选择器优化 */
@media (max-width: 768px) {
  input[type="color"] {
    height: 48px; /* 在移动端进一步增加高度 */
    border-radius: 12px;
    border-width: 2px; /* 增加边框宽度，提高可见性 */
  }
}

/* File input styling */
.custom-file-label {
  background: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-secondary) !important;
  border-radius: 8px !important;
  padding: 0.625rem 0.875rem !important;
  font-size: 0.875rem !important;
  min-height: 38px !important;
  display: flex !important;
  align-items: center !important;
}

/* -------------------- Buttons -------------------- */
.btn {
  border-radius: 10px; /* 增加圆角，提高现代感 */
  font-weight: 500;
  padding: 0.75rem 1.5rem; /* 增加内边距，提高触摸友好性 */
  transition: all 0.2s ease;
  border: none;
  font-size: 0.9rem; /* 稍微增大字体 */
  letter-spacing: 0.025em;
  position: relative;
  overflow: hidden;
  min-height: 44px; /* 增加最小高度，确保触摸目标大小 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--gradient-primary);
  color: #1a2332;
  font-weight: 700;
  box-shadow: 0 6px 20px rgba(251, 191, 36, 0.4);
  border: 2px solid transparent;
}

.btn-primary:hover {
  background: var(--gradient-primary);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 10px 30px rgba(251, 191, 36, 0.6);
  border-color: var(--yugioh-gold);
  color: #0d1421;
}

.btn-success {
  background: var(--gradient-secondary);
  color: white;
  font-weight: 700;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.btn-success:hover {
  background: linear-gradient(135deg, var(--yugioh-dark-blue), var(--yugioh-blue));
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.6);
}

.btn-info {
  background: linear-gradient(135deg, var(--yugioh-purple), var(--accent-secondary));
  color: white;
  font-weight: 700;
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

.btn-info:hover {
  background: linear-gradient(135deg, #7c3aed, var(--yugioh-purple));
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 10px 30px rgba(139, 92, 246, 0.6);
}

.btn-outline-danger {
  background: transparent;
  border: 2px solid var(--accent-danger);
  color: var(--accent-danger);
}

.btn-outline-danger:hover {
  background: var(--accent-danger);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.3);
}

.btn-sm {
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
}

/* -------------------- Anchor Navigation Fix -------------------- */
.anchor-section {
  scroll-margin-top: 100px; /* 为固定导航栏留出空间 */
}

/* -------------------- Sections -------------------- */
.features-section,
.what-is-section,
.why-use-section,
.how-to-section,
.how-to-use-section,
.faq-section {
  padding: 4rem 0;
  border-top: 1px solid var(--border-color);
}

.features-section,
.why-use-section,
.how-to-use-section,
.faq-section {
  background: var(--bg-secondary);
}

.what-is-section,
.how-to-section {
  background: var(--bg-primary);
}

.section-title {
  color: var(--text-primary);
  font-weight: 700;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  line-height: 1.2; /* 改善行高 */
}

.section-subtitle {
  color: var(--text-secondary);
  font-size: 1.2rem;
  margin-bottom: 1rem;
  line-height: 1.4; /* 改善行高 */
}

.section-description {
  font-size: 1rem;
  color: var(--text-muted);
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto 2rem;
}

/* 移动端标题和描述优化 */
@media (max-width: 768px) {
  .section-title {
    font-size: 2rem; /* 减小移动端标题字体 */
    margin-bottom: 0.75rem;
    text-align: center; /* 移动端居中对齐 */
  }

  .section-subtitle {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
    text-align: center;
  }

  .section-description {
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
    text-align: center;
    padding: 0 1rem; /* 添加水平内边距 */
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 1.75rem; /* 进一步减小超小屏幕标题字体 */
    margin-bottom: 0.5rem;
  }

  .section-subtitle {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .section-description {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    padding: 0 0.5rem;
  }
}

/* -------------------- What Is Section -------------------- */

.feature-highlights {
  margin-top: 2rem;
}

.highlight-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.highlight-icon {
  color: var(--yugioh-gold);
  margin-right: 1rem;
  font-size: 1.2rem;
}

.info-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.visual-card {
  background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  border: 1px solid var(--border-color);
  max-width: 400px;
}

.visual-icon {
  font-size: 4rem;
  color: var(--yugioh-gold);
  margin-bottom: 1.5rem;
}

/* -------------------- Why Use Section -------------------- */

.reason-card {
  background: var(--bg-card);
  border: 1px solid rgba(55, 65, 81, 0.2);
  border-radius: 20px;
  padding: 2.5rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  height: 100%;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.reason-card:hover {
  transform: translateY(-10px);
  border-color: var(--accent-primary);
  box-shadow: 0 25px 50px rgba(59, 130, 246, 0.15);
}

.reason-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
  font-size: 1.5rem;
}

.reason-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  margin-top: 0.5rem;
  flex-shrink: 0;
}

.reason-description {
  color: var(--text-secondary);
  line-height: 1.6;
  flex-grow: 1;
  font-size: 0.95rem;
}

/* -------------------- How To Section -------------------- */

.step-card {
  background: var(--bg-card);
  border: 1px solid rgba(55, 65, 81, 0.2);
  border-radius: 20px;
  padding: 2.5rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  height: 100%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: relative;
}

.step-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.step-number {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  background: var(--yugioh-gold);
  color: var(--bg-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);
}

.step-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1.5rem 0 1rem;
  font-size: 1.5rem;
  color: white;
}

.step-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.step-description {
  color: var(--text-secondary);
  line-height: 1.6;
  flex-grow: 1;
}

/* -------------------- Feature Cards -------------------- */
.feature-card {
  background: var(--bg-card);
  border: 1px solid rgba(55, 65, 81, 0.2);
  border-radius: 20px;
  padding: 2.5rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  height: 100%;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

/* 移动端功能卡片优化 */
@media (max-width: 768px) {
  .feature-card {
    padding: 2rem 1.5rem; /* 减少内边距 */
    border-radius: 16px; /* 减小圆角 */
    margin-bottom: 1.5rem;
  }

  .feature-icon {
    width: 50px !important;
    height: 50px !important;
    font-size: 1.25rem !important;
    margin-bottom: 1rem !important;
  }

  .feature-title {
    font-size: 1.1rem !important;
    margin-bottom: 0.75rem !important;
  }

  .feature-description {
    font-size: 0.9rem !important;
    line-height: 1.5 !important;
  }
}

@media (max-width: 480px) {
  .feature-card {
    padding: 1.5rem 1rem; /* 进一步减少内边距 */
    border-radius: 12px;
    margin-bottom: 1rem;
  }

  .feature-icon {
    width: 45px !important;
    height: 45px !important;
    font-size: 1.1rem !important;
  }

  .feature-title {
    font-size: 1rem !important;
    margin-bottom: 0.5rem !important;
  }

  .feature-description {
    font-size: 0.85rem !important;
  }
}

.feature-card:hover {
  transform: translateY(-10px);
  border-color: var(--accent-primary);
  box-shadow: 0 25px 50px rgba(59, 130, 246, 0.15);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
  font-size: 1.5rem;
}

.feature-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  margin-top: 0.5rem;
  flex-shrink: 0;
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 0.95rem;
  margin-bottom: 0;
  flex-grow: 1;
  display: flex;
  align-items: center;
  text-align: center;
}

/* -------------------- Step Cards -------------------- */
.step-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  height: 100%;
}

.step-card:hover {
  transform: translateY(-8px);
  border-color: var(--accent-secondary);
  box-shadow: 0 20px 40px rgba(16, 185, 129, 0.1);
}

.step-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--accent-secondary), #059669);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
}

.step-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.step-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* -------------------- FAQ -------------------- */
.faq-list {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
  overflow: hidden;
}

/* 移动端FAQ优化 */
@media (max-width: 768px) {
  .faq-item {
    margin-bottom: 1rem;
    border-radius: 10px;
  }

  .faq-question {
    padding: 1rem !important; /* 增加触摸区域 */
    font-size: 0.95rem;
  }

  .faq-question-text {
    font-size: 0.95rem !important;
    line-height: 1.4;
    padding-right: 2rem; /* 为图标留出空间 */
  }

  .faq-toggle-icon {
    font-size: 1rem;
    min-width: 24px; /* 确保图标有足够的触摸区域 */
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .faq-answer {
    padding: 1rem !important;
    font-size: 0.9rem;
    line-height: 1.5;
  }
}

@media (max-width: 480px) {
  .faq-item {
    margin-bottom: 0.75rem;
    border-radius: 8px;
  }

  .faq-question {
    padding: 0.75rem !important;
  }

  .faq-question-text {
    font-size: 0.9rem !important;
    padding-right: 1.5rem;
  }

  .faq-toggle-icon {
    font-size: 0.9rem;
    min-width: 20px;
    height: 20px;
  }

  .faq-answer {
    padding: 0.75rem !important;
    font-size: 0.85rem;
  }
}

.faq-item:hover {
  border-color: var(--accent-primary);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
}

.faq-item.active {
  border-color: var(--accent-primary);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0;
  user-select: none;
}

.faq-question:hover,
.faq-question:focus {
  background: rgba(59, 130, 246, 0.05);
  outline: none;
}

.faq-question:focus {
  box-shadow: inset 0 0 0 2px var(--accent-primary);
}

.faq-question-text {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.1rem;
  margin: 0;
  flex: 1;
  padding-right: 1rem;
}

.faq-toggle-icon {
  color: var(--accent-primary);
  font-size: 1rem;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.faq-item.active .faq-toggle-icon {
  transform: rotate(180deg);
}

.faq-answer-wrapper {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              padding 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s ease;
  opacity: 0;
}

.faq-answer-wrapper.expanded {
  max-height: 600px; /* 足够大的值来容纳答案 */
  padding: 0 2rem 1.5rem 2rem;
  opacity: 1;
}

.faq-answer {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
  padding-top: 0.5rem;
}

/* -------------------- Footer Styles -------------------- */
.site-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  color: var(--text-primary);
}

.footer-brand {
  margin-bottom: 2rem;
}

.footer-title {
  font-weight: 700;
  color: var(--text-primary);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

.footer-links-title {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 1rem;
}

.footer-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-nav li {
  margin-bottom: 0.5rem;
}

.footer-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-block;
}

.footer-link:hover {
  color: var(--accent-primary);
  text-decoration: none;
  transform: translateX(4px);
}

.footer-disclaimer {
  padding: 1rem;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border-left: 3px solid var(--accent-primary);
}

.footer-divider {
  border-color: var(--border-color);
  opacity: 0.5;
}

.copyright {
  color: var(--text-muted);
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .footer-brand {
    text-align: center;
    margin-bottom: 2rem;
  }

  .footer-links {
    text-align: center;
  }
}

/* -------------------- Responsive Design -------------------- */
@media (max-width: 1200px) {
  .preview-panel {
    min-height: 75vh;
    max-height: 75vh; /* Increased from 70vh for better preview visibility */
  }

  .editor-panel {
    min-height: 75vh;
    max-height: 75vh; /* Increased from 70vh to match preview panel */
  }

  .preview-container {
    max-width: 420px; /* Slightly larger on medium screens */
  }
}

@media (max-width: 992px) {
  .card-editor-section {
    padding: 1rem 0;
  }

  /* 保持Bootstrap网格系统的居中行为，不强制改变flex-direction */
  .card-editor-section .row {
    /* 让Bootstrap处理响应式布局，不强制改变flex-direction */
    flex-wrap: wrap;
  }

  .editor-panel {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    min-height: auto;
    max-height: none;
    margin-bottom: 1rem;
    position: relative;
    top: auto;
  }

  .preview-panel {
    padding: 1rem;
    min-height: 480px; /* 增加最小高度 */
    max-height: 70vh; /* 增加最大高度，提供更好的预览体验 */
    position: relative;
    top: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .preview-container {
    width: 100%;
    max-width: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .scrollable-content {
    padding: 1rem;
  }

  .fixed-bottom-controls {
    padding: 1rem;
  }

  .section-title {
    font-size: 2rem;
  }
}

/* Medium screens (tablets) */
@media (max-width: 992px) {
  .feature-card,
  .reason-card,
  .step-card {
    margin-bottom: 1.5rem;
    padding: 2rem 1.5rem;
  }

  .features-section .row.g-4,
  .why-use-section .row.g-4,
  .how-to-section .row.g-4 {
    gap: 1.5rem;
  }

  .visual-card {
    margin-top: 2rem;
    padding: 2rem 1.5rem;
  }

  .reason-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .reason-title {
    font-size: 1.125rem;
  }

  .reason-card {
    min-height: 250px;
  }

  .highlight-item {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  /* 确保页面内容不被固定header遮挡 */
  body {
    padding-top: 64px; /* 调整为与logo高度一致 */
  }

  .main-navbar {
    min-height: 64px; /* 与logo高度保持一致，更简洁 */
    padding: 0.5rem 1rem; /* 减少垂直内边距 */
    justify-content: space-between;
    align-items: center; /* 确保垂直居中 */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
    width: 100%;
    display: flex;
  }

  /* 右侧控制区域 */
  .navbar-right-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
  }





  /* 语言切换器在移动端的样式调整 */
  .language-switcher {
    flex-shrink: 0;
  }

  /* Mobile-optimized brand styling */
  .brand-container {
    flex-direction: row;
    align-items: center;
    gap: 0.75rem;
    flex: 1; /* 允许品牌区域占用可用空间 */
    min-width: 0; /* 防止flex项目溢出 */
    max-width: calc(100vw - 120px); /* 为语言切换器留出空间 */
    overflow: hidden;
  }

  .brand-logo {
    height: 48px; /* 调整logo高度与header高度匹配 */
    width: auto;
    flex-shrink: 0;
    border-radius: 6px; /* 减小圆角适应移动端 */
    object-fit: contain;
  }

  .brand-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0; /* 防止文字溢出 */
    flex: 1;
  }

  .brand-main {
    font-size: 1.15rem; /* 增大字体 */
    font-weight: 700;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-primary);
    max-width: 100%;
  }

  .brand-sub {
    font-size: 0.75rem; /* 增大副标题字体 */
    opacity: 0.85;
    margin-top: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-secondary);
    max-width: 100%;
  }

  /* Ensure language switcher is properly positioned */
  .ml-auto {
    margin-left: auto !important;
    flex-shrink: 0;
    min-width: 90px; /* 增加语言切换器空间 */
  }

  /* 移动端卡片预览优化 */
  .card-preview-wrapper {
    background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
    border-radius: 20px;
    padding: 2rem 1.25rem; /* 增加内边距保持层次感 */
    box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 var(--shadow-light),
      0 0 0 2px var(--yugioh-gold),
      0 0 8px rgba(251, 191, 36, 0.15);
    border: 2px solid transparent;
  }

  /* 禁用移动端的hover效果 */
  .card-display:hover {
    transform: none;
  }

  .card-display:hover .card-canvas {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6), 0 5px 15px rgba(0, 0, 0, 0.4);
  }

  /* 平板设备优化 */
  .card-editor-section .container-fluid {
    padding: 0 1rem;
  }

  .preview-panel {
    min-height: 70vh;
    max-height: 70vh;
  }

  .card-preview-wrapper {
    padding: 1.5rem 1rem;
  }

  /* 确保表单在平板上有合适的间距 */
  .scrollable-content {
    padding: 1.25rem;
  }

  .fixed-bottom-controls {
    padding: 1.25rem;
  }

  .form-control,
  .form-select {
    font-size: 16px; /* 防止iOS Safari缩放 */
    min-height: 44px; /* 确保触摸友好的最小高度 */
  }

  /* 移动端按钮优化 */
  .btn {
    min-height: 44px; /* 触摸友好的最小高度 */
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }

  /* 移动端卡片交互优化 */
  .card-display {
    cursor: default; /* 移动端不显示指针 */
    -webkit-tap-highlight-color: transparent; /* 移除点击高亮 */
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  /* 防止水平滚动 */
  body {
    overflow-x: hidden;
    padding-top: 60px; /* 调整为更小的header高度 */
  }

  .main-navbar {
    padding: 0.5rem 0.75rem; /* 保持内边距 */
    min-height: 60px; /* 确保最小高度 */
  }

  .brand-container {
    gap: 0.5rem;
    flex: 1;
    min-width: 0;
  }

  .brand-logo {
    height: 42px; /* 稍微增加高度 */
    border-radius: 4px;
  }

  .brand-text {
    min-width: 0;
    flex: 1;
  }

  .brand-main {
    font-size: 0.95rem; /* 稍微增大字体 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .brand-sub {
    font-size: 0.65rem; /* 稍微增大副标题字体 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 确保语言切换器在超小屏幕上可用 */
  .ml-auto {
    min-width: 70px;
  }

  .card-editor-section {
    padding: 0.5rem 0;
    margin-top: 70px;
  }

  .card-editor-section .container-fluid {
    padding: 0 0.5rem; /* 减少内边距防止溢出 */
    max-width: 100%;
  }

  .card-editor-section .row {
    margin: 0;
    /* 保持Bootstrap的justify-content-center类的效果 */
  }

  .card-editor-section .col-lg-6 {
    padding: 0.25rem; /* 进一步减少内边距 */
    max-width: 100%;
  }

  .scrollable-content {
    padding: 0.5rem;
  }

  .fixed-bottom-controls {
    padding: 0.75rem 0.5rem;
  }

  .preview-panel {
    padding: 0.5rem;
    max-height: 60vh; /* 在小屏幕上减少高度 */
    min-height: 350px; /* 减少最小高度 */
    position: relative;
    top: auto;
  }

  .card-preview-wrapper {
    max-width: 100%;
    overflow: hidden;
    padding: 1rem 0.5rem; /* 大幅减少内边距 */
    margin: 0 auto;
  }

  .card-preview-wrapper canvas {
    max-width: 100%;
    width: 100%;
    height: auto;
    object-fit: contain;
  }

  /* 确保卡片显示区域不会溢出 */
  .card-display {
    max-width: 100%;
    overflow: hidden;
  }

  #yugiohcard {
    max-width: 100% !important;
    height: auto !important;
  }



  .preview-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .preview-actions {
    justify-content: center;
  }

  .brand-main {
    font-size: 1.2rem;
  }

  .brand-sub {
    font-size: 0.7rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 576px) {
  /* 防止水平滚动 */
  html, body {
    overflow-x: hidden;
    max-width: 100%;
  }

  .form-section {
    padding: 0.75rem;
  }

  .card-preview-wrapper {
    padding: 1rem 0.5rem; /* 进一步优化小屏幕内边距 */
    margin: 0;
  }

  .feature-card,
  .reason-card,
  .step-card {
    padding: 1.25rem;
    margin-bottom: 1.25rem;
  }

  .faq-item {
    margin-bottom: 0.75rem;
    border-radius: 8px;
  }

  .faq-question {
    padding: 0.75rem 1rem;
  }

  /* 表单控件优化 */
  .form-control,
  .form-select {
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 按钮组优化 */
  .button-group {
    flex-wrap: wrap;
    gap: 0.5rem !important;
  }

  .button-group .btn {
    flex: 1 1 auto;
    min-width: 120px;
    max-width: 100%;
  }

  .faq-question-text {
    font-size: 0.95rem;
    padding-right: 0.75rem;
  }

  .faq-toggle-icon {
    font-size: 0.9rem;
  }

  .faq-answer-wrapper.expanded {
    padding: 0 1.25rem 1rem 1.25rem;
    max-height: 400px;
  }

  .faq-answer {
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .visual-card {
    padding: 1.5rem 1rem;
    margin-top: 1.5rem;
  }

  .visual-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .reason-icon,
  .step-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .step-number {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .highlight-item {
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
  }

  .reason-card {
    min-height: 220px;
  }

  .feature-card,
  .reason-card {
    min-height: 280px;
  }

  .feature-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .feature-title {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
  }

  .feature-description {
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .section-subtitle {
    font-size: 0.95rem;
  }
}

/* -------------------- Accessibility -------------------- */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (prefers-contrast: high) {
  :root {
    --bg-primary: #000000;
    --bg-secondary: #111111;
    --bg-tertiary: #222222;
    --bg-card: #111111;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --border-color: #666666;
    --accent-primary: #4da6ff;
    --accent-secondary: #4dff88;
  }

  .form-control,
  .form-select,
  .feature-card,
  .step-card,
  .faq-item {
    border-width: 2px;
  }
}

/* -------------------- Focus Styles -------------------- */
.btn:focus,
.form-control:focus,
.form-select:focus,
.nav-link-custom:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* -------------------- Scrollbar Styling -------------------- */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* -------------------- Loading States -------------------- */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* -------------------- Animations -------------------- */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card,
.step-card,
.faq-item {
  animation: fadeInUp 0.6s ease-out;
}

/* -------------------- Dark Theme System Preference -------------------- */
@media (prefers-color-scheme: light) {
  /* Users can still use dark theme as it's the design choice */
}

/* -------------------- Print Styles -------------------- */
@media print {
  .main-navbar,
  .preview-actions,
  .editor-panel {
    display: none !important;
  }

  .preview-panel {
    width: 100% !important;
    max-width: none !important;
  }
}

/* -------------------- 卡片區樣式 -------------------- */
.padding-transition {
  transition: all .5s linear;
}
#yugiohcard-wrap {
  transition: transform 0.1s ease;
  transform-style: preserve-3d;
  will-change: transform;
}
#yugiohcard-wrap:hover #yugiohcard {
  transform: translateZ(12px);
}
#yugiohcard {
  transition: transform 0.3s ease;
}

/* -------------------- 輸入區樣式 -------------------- */
/* 輸入區底色 */
select, textarea, input, .custom-file-label{
	background-color: #7777774A !important;
	color: #ccc !important;
	border: 0 !important;
}

/* 文本区域移动端优化 */
textarea {
  resize: vertical; /* 只允许垂直调整大小 */
  min-height: 120px; /* 设置最小高度 */
  font-size: 0.9rem;
  line-height: 1.5;
}

@media (max-width: 768px) {
  textarea {
    min-height: 100px; /* 移动端减少最小高度 */
    font-size: 16px; /* 防止iOS缩放 */
    padding: 0.75rem; /* 增加内边距 */
    border-radius: 10px;
  }
}
/* 下拉選單icon */
.custom-select {
  background-image: var(--chevron-down-svg-path);
}
/* 下拉區底色 */
select option {
  background: #666666;
  color: #fff;
}
/* Checkbox styling */
.checkbox-wrap {
  width: 100%;
}
.checkbox-wrap > label {
  width: 100%;
  text-align: left;
  border: 1px solid var(--border-color) !important;
  color: var(--text-secondary) !important;
  background-color: var(--bg-tertiary) !important;
  border-radius: 10px !important; /* 增加圆角 */
  padding: 0.75rem 1rem !important; /* 增加内边距，提高触摸友好性 */
  font-size: 0.9rem !important; /* 增大字体 */
  font-weight: 400 !important;
  transition: all 0.2s ease !important;
  min-height: 44px !important; /* 增加最小高度，符合触摸目标标准 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important; /* 居中对齐 */
  cursor: pointer !important; /* 添加指针样式 */
}
.checkbox-wrap.active > label {
  color: var(--text-primary) !important;
  background-color: var(--yugioh-gold) !important;
  border-color: var(--yugioh-gold) !important;
}
/* 檔案上傳鈕 */
.custom-file-label::after {
  content: '✚' !important;
	background-color: #787878 !important;
	color: #FFF;
}

/* -------------------- Cross-browser compatibility and height fixes -------------------- */
/* Ensure consistent height across browsers */
.preview-panel,
.editor-panel {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

/* Fix for Safari sticky positioning */
@supports (-webkit-appearance: none) {
  .preview-panel,
  .editor-panel {
    position: -webkit-sticky;
    position: sticky;
  }
}

/* Ensure backdrop-filter works across browsers */
.main-navbar {
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}

/* Fix for Firefox flexbox height issues */
@-moz-document url-prefix() {
  .preview-panel,
  .editor-panel {
    min-height: -moz-calc(85vh);
    max-height: -moz-calc(85vh);
  }
}



/* -------------------- Accessibility improvements -------------------- */
/* Focus indicators for better keyboard navigation */
.reason-card:focus,
.step-card:focus,
.feature-card:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .reason-card,
  .step-card,
  .feature-card {
    border: 2px solid var(--text-primary);
  }

  .reason-icon,
  .step-icon {
    background: var(--text-primary);
    color: var(--bg-primary);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .reason-card,
  .step-card,
  .feature-card,
  .visual-card {
    transition: none;
  }

  .reason-card:hover,
  .step-card:hover,
  .feature-card:hover {
    transform: none;
  }
}

/* 按鈕組響應式布局 */
.button-group {
  gap: 0.5rem !important;
}

.main-buttons {
  flex: 1;
  min-width: 0;
}

/* 社交分享行样式 */
.social-share-row {
  margin-top: 0.5rem;
}

/* 在中等屏幕上的布局优化 */
@media (max-width: 991px) and (min-width: 768px) {
  .button-group {
    justify-content: center !important;
  }

  .social-share-row {
    justify-content: center !important;
  }
}

/* 在小屏幕上確保所有按鈕居中對齊 */
@media (max-width: 767px) {
  .button-group {
    justify-content: center !important;
  }

  .social-share-row {
    justify-content: center !important;
    margin-top: 0.75rem; /* 在小屏幕上增加间距 */
  }
}

/* ==================== 响应式设计系统 ==================== */

/* 通用响应式优化 - 防止水平滚动 */
* {
  box-sizing: border-box;
}

html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

.container-fluid,
.row,
.col,
[class*="col-"] {
  max-width: 100%;
}

/* 确保所有图片和媒体元素响应式 */
img,
canvas,
video {
  max-width: 100%;
  height: auto;
}

/* 确保表单元素不会溢出 */
.form-control,
.form-select,
.form-check-input,
input,
textarea,
select {
  max-width: 100%;
  box-sizing: border-box;
}

/* 1. 超小屏幕：320px-375px (小型手机) */
@media (max-width: 375px) {
  /* 防止水平滚动 */
  html, body {
    overflow-x: hidden;
    max-width: 100vw;
  }

  .card-editor-section .container-fluid {
    padding: 0 0.25rem;
    max-width: 100%;
  }

  .card-editor-section .row {
    margin: 0;
  }

  .card-editor-section .col-lg-6 {
    padding: 0.125rem;
  }

  .card-preview-wrapper {
    padding: 1.25rem 0.75rem; /* 保持足够的内边距维持层次感 */
    margin: 0;
    border-radius: 16px; /* 减小圆角，适应小屏幕 */
    background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
    box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 var(--shadow-light),
      0 0 0 1px var(--yugioh-gold),
      0 0 6px rgba(251, 191, 36, 0.1);
    border: 1px solid transparent;
  }

  .scrollable-content {
    padding: 0.25rem;
  }

  .fixed-bottom-controls {
    padding: 0.5rem 0.25rem;
  }

  .preview-panel {
    padding: 0.75rem; /* 增加内边距 */
    min-height: 350px; /* 增加最小高度，确保卡片完整显示 */
    max-height: 60vh; /* 增加最大高度 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .preview-container {
    width: 100%;
    max-width: 300px; /* 增加最大宽度，提供更好的预览体验 */
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.5rem;
  }

  .brand-main {
    font-size: 0.75rem;
  }

  .brand-sub {
    font-size: 0.5rem;
  }

  .button-group .btn {
    min-width: 100px; /* 增加最小宽度 */
    font-size: 0.8rem; /* 稍微增大字体 */
    padding: 0.5rem 0.75rem; /* 增加内边距 */
    min-height: 44px; /* 确保触摸目标大小 */
    border-radius: 6px; /* 适应超小屏幕 */
    flex: 1 1 auto; /* 允许按钮自适应宽度 */
    max-width: 140px; /* 限制最大宽度 */
  }

  /* 超小屏幕按钮组布局优化 */
  .button-group {
    flex-direction: column; /* 在超小屏幕上垂直排列 */
    gap: 0.5rem !important;
    width: 100%;
  }

  .button-group .btn {
    width: 100%; /* 按钮占满宽度 */
    max-width: none;
    min-width: auto;
  }

  /* 确保表单元素不会溢出 */
  .form-control,
  .form-select,
  .form-check-input {
    max-width: 100%;
    font-size: 16px; /* 防止iOS缩放，从14px改为16px */
    min-height: 48px; /* 在超小屏幕上增加最小高度 */
    padding: 0.75rem 0.875rem; /* 增加内边距 */
    border-radius: 8px; /* 适当减小圆角 */
  }

  /* 超小屏幕标签优化 */
  label {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    white-space: normal;
    overflow: visible;
    text-overflow: initial;
  }

  /* 超小屏幕复选框优化 */
  .checkbox-wrap > label {
    min-height: 48px !important;
    padding: 0.75rem !important;
    font-size: 0.85rem !important;
  }

  /* 优化卡片预览在超小屏幕上的显示 */
  .card-display {
    transform: scale(0.95); /* 增加缩放比例，提供更好的可见性 */
    transform-origin: center;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0.5rem 0;
  }

  #yugiohcard {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 12px; /* 减小圆角适应小屏幕 */
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4); /* 增强阴影效果 */
  }
}

/* 2. 小屏幕：376px-576px (标准手机) */
@media (min-width: 376px) and (max-width: 576px) {
  .card-editor-section .container-fluid {
    padding: 0 0.5rem;
  }

  .scrollable-content {
    padding: 0.5rem;
  }

  .fixed-bottom-controls {
    padding: 0.75rem 0.5rem;
  }

  .preview-panel {
    min-height: 380px; /* 增加最小高度 */
    max-height: 60vh; /* 增加最大高度 */
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .preview-container {
    width: 100%;
    max-width: 320px; /* 适应标准手机屏幕 */
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .card-preview-wrapper {
    padding: 1.75rem 1rem; /* 增加内边距保持层次感 */
    border-radius: 18px;
    background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
    box-shadow:
      0 12px 35px rgba(0, 0, 0, 0.35),
      inset 0 1px 0 var(--shadow-light),
      0 0 0 1.5px var(--yugioh-gold),
      0 0 8px rgba(251, 191, 36, 0.12);
    border: 1.5px solid transparent;
  }

  .button-group .btn {
    min-width: 100px;
    font-size: 0.8rem;
  }

  .card-display {
    transform: scale(0.95); /* 从0.9调整为0.95 */
    transform-origin: center;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  #yugiohcard {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 14px;
  }
}

/* 3. 中等屏幕：577px-768px (大屏手机/小平板) */
@media (min-width: 577px) and (max-width: 768px) {
  .card-editor-section .container-fluid {
    padding: 0 0.75rem;
  }

  .scrollable-content {
    padding: 0.75rem;
  }

  .fixed-bottom-controls {
    padding: 1rem 0.75rem;
  }

  .preview-panel {
    min-height: 450px; /* 增加最小高度 */
    max-height: 70vh; /* 增加最大高度 */
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .preview-container {
    width: 100%;
    max-width: 400px; /* 增加最大宽度，适应大屏手机 */
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.75rem;
  }

  .card-preview-wrapper {
    padding: 1.5rem 1rem; /* 增加内边距 */
    border-radius: 20px;
    background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
  }

  .button-group .btn {
    min-width: 110px;
  }

  .card-display {
    transform: scale(1); /* 从0.95调整为1，在大屏手机上显示原始大小 */
    transform-origin: center;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  #yugiohcard {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 16px;
  }
}

/* 4. 大屏幕：769px-1024px (平板) */
@media (min-width: 769px) and (max-width: 1024px) {
  .card-editor-section .container-fluid {
    padding: 0 1rem;
  }

  .scrollable-content {
    padding: 1rem;
  }

  .fixed-bottom-controls {
    padding: 1.25rem 1rem;
  }

  .preview-panel {
    min-height: 520px; /* 增加最小高度 */
    max-height: 75vh; /* 增加最大高度 */
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .preview-container {
    width: 100%;
    max-width: 450px; /* 适应平板屏幕 */
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .card-preview-wrapper {
    padding: 2rem 1.25rem; /* 增加内边距 */
    border-radius: 22px;
    background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
    box-shadow: 0 18px 50px rgba(0, 0, 0, 0.45);
  }

  .button-group .btn {
    min-width: 120px;
  }

  .card-display {
    transform: scale(1);
    transform-origin: center;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  #yugiohcard {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 16px;
  }
}

/* 5. 超大屏幕：1025px以上 (桌面) */
@media (min-width: 1025px) {
  .card-editor-section .container-fluid {
    max-width: 1200px;
    padding: 0 1.5rem;
  }

  .scrollable-content {
    padding: 1.5rem;
  }

  .fixed-bottom-controls {
    padding: 1.5rem;
  }

  .preview-panel {
    min-height: 600px;
    max-height: 80vh;
  }

  .card-preview-wrapper {
    padding: 2rem 1.5rem;
  }

  .button-group .btn {
    min-width: 130px;
  }

  .card-display {
    transform: scale(1);
    transform-origin: center;
  }
}

/* -------------------- 移动端横屏优化 -------------------- */
@media (max-width: 768px) and (orientation: landscape) {
  body {
    padding-top: 60px; /* 横屏时减少顶部间距 */
  }

  .main-navbar {
    min-height: 60px; /* 横屏时减少header高度 */
    padding: 0.5rem 1rem;
  }

  .brand-logo {
    height: 45px; /* 横屏时减少logo高度 */
  }

  .brand-main {
    font-size: 1rem;
  }

  .brand-sub {
    font-size: 0.7rem;
  }

  .preview-panel {
    min-height: 300px;
    max-height: 80vh; /* 横屏时增加最大高度 */
    padding: 0.75rem;
  }

  .card-display {
    transform: scale(0.85); /* 横屏时适当缩小 */
  }
}

/* -------------------- 表单布局优化 -------------------- */
.link-table {
  width: 100%;
  max-width: 120px;
  margin: 0 auto;
}

.link-table td {
  padding: 2px;
  text-align: center;
}

.link-table .checkbox-wrap {
  min-width: 30px;
  min-height: 30px;
  font-size: 0.8rem;
  padding: 0.25rem;
}

/* 表单间距优化 */
.form-group {
  margin-bottom: 1rem;
}

.form-control, .form-select {
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.form-control:focus, .form-select:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--accent-primary-rgb), 0.25);
}

/* 表单行间距优化 */
.my-3 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

/* 表单标签优化 */
label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-size: 0.9rem;
}

/* 复选框按钮优化 */
.checkbox-wrap {
  transition: all 0.3s ease;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
}

.checkbox-wrap:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 响应式表单优化 */
@media (max-width: 576px) {
  .link-table {
    max-width: 100px;
  }

  .link-table .checkbox-wrap {
    min-width: 25px;
    min-height: 25px;
    font-size: 0.7rem;
    padding: 0.2rem;
  }
}

/* -------------------- 超宽屏幕优化 -------------------- */
@media (min-width: 1400px) {
  .preview-panel {
    min-height: 90vh;
    max-height: 90vh;
  }

  .card-display {
    transform: scale(1.1); /* 超宽屏幕时放大卡片 */
  }
}
</style>
